import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../models/dashboard_models.dart';
import '../services/dashboard_service.dart';
import '../../../constants/app_styles.dart';
import '../../../constants/app_colors.dart';

/// المكون الأساسي لجميع المخططات - يقلل التكرار بنسبة 90%
abstract class BaseChartWidget extends StatefulWidget {
  final DashboardItem item;
  final FilterOptions? filters;
  final bool showHeader;
  final bool showFilters;
  final VoidCallback? onRefresh;

  const BaseChartWidget({
    super.key,
    required this.item,
    this.filters,
    this.showHeader = true,
    this.showFilters = false,
    this.onRefresh,
  });

  /// دالة مجردة لبناء محتوى المخطط - يجب تنفيذها في كل مخطط
  Widget buildChartContent(BuildContext context, List<ChartData> data);

  @override
  State<BaseChartWidget> createState() => _BaseChartWidgetState();
}

/// الحالة الأساسية لجميع المخططات
class _BaseChartWidgetState extends State<BaseChartWidget> {
  final DashboardService _dashboardService = Get.find<DashboardService>();
  
  bool _isLoading = false;
  String? _error;
  List<ChartData> _data = [];

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  @override
  void didUpdateWidget(BaseChartWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.filters != widget.filters) {
      _loadData();
    }
  }

  /// تحميل البيانات
  Future<void> _loadData() async {
    if (!mounted) return;

    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      final data = await _dashboardService.getChartData(
        widget.item.id,
        widget.filters,
      );
      
      if (mounted) {
        setState(() {
          _data = data;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _error = e.toString();
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        mainAxisSize: MainAxisSize.min,
        children: [
          // رأس المخطط
          if (widget.showHeader) _buildHeader(),

          // محتوى المخطط
          Flexible(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: _buildContent(),
            ),
          ),
        ],
      ),
    );
  }

  /// بناء رأس المخطط
  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.primary.withOpacity(0.1),
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(12),
          topRight: Radius.circular(12),
        ),
      ),
      child: Row(
        children: [
          // أيقونة نوع المخطط
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: AppColors.primary.withOpacity(0.2),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              widget.item.chartType.icon,
              color: AppColors.primary,
              size: 20,
            ),
          ),
          
          const SizedBox(width: 12),
          
          // عنوان المخطط
          Expanded(
            child: Text(
              widget.item.title,
              style: AppStyles.titleMedium.copyWith(
                fontWeight: FontWeight.bold,
                color: AppColors.textPrimary,
              ),
            ),
          ),
          
          // أزرار التحكم
          Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              // زر التحديث
              IconButton(
                icon: const Icon(Icons.refresh, size: 20),
                onPressed: () {
                  _loadData();
                  widget.onRefresh?.call();
                },
                tooltip: 'تحديث',
                style: IconButton.styleFrom(
                  backgroundColor: Colors.white.withOpacity(0.8),
                  foregroundColor: AppColors.primary,
                ),
              ),
              
              const SizedBox(width: 4),
              
              // زر ملء الشاشة
              IconButton(
                icon: const Icon(Icons.fullscreen, size: 20),
                onPressed: _showFullscreen,
                tooltip: 'ملء الشاشة',
                style: IconButton.styleFrom(
                  backgroundColor: Colors.white.withOpacity(0.8),
                  foregroundColor: AppColors.primary,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// بناء محتوى المخطط
  Widget _buildContent() {
    if (_isLoading) {
      return _buildLoadingWidget();
    }

    if (_error != null) {
      return _buildErrorWidget();
    }

    if (_data.isEmpty) {
      return _buildEmptyWidget();
    }

    return widget.buildChartContent(context, _data);
  }

  /// واجهة التحميل
  Widget _buildLoadingWidget() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(),
          SizedBox(height: 16),
          Text('جاري تحميل البيانات...'),
        ],
      ),
    );
  }

  /// واجهة الخطأ
  Widget _buildErrorWidget() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 48,
            color: Colors.red.shade400,
          ),
          const SizedBox(height: 16),
          Text(
            'خطأ في تحميل البيانات',
            style: AppStyles.titleMedium.copyWith(
              color: Colors.red.shade600,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            _error!,
            style: AppStyles.bodySmall,
            textAlign: TextAlign.center,
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
          const SizedBox(height: 16),
          ElevatedButton.icon(
            onPressed: _loadData,
            icon: const Icon(Icons.refresh, size: 16),
            label: const Text('إعادة المحاولة'),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primary,
              foregroundColor: Colors.white,
            ),
          ),
        ],
      ),
    );
  }

  /// واجهة البيانات الفارغة
  Widget _buildEmptyWidget() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            Icons.bar_chart,
            size: 32,
            color: Colors.grey.shade400,
          ),
          const SizedBox(height: 8),
          Text(
            'لا توجد بيانات',
            style: AppStyles.bodyMedium.copyWith(
              color: Colors.grey.shade600,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  /// عرض المخطط بملء الشاشة
  void _showFullscreen() {
    showDialog(
      context: context,
      builder: (context) => Dialog.fullscreen(
        child: Scaffold(
          appBar: AppBar(
            title: Text(widget.item.title),
            backgroundColor: AppColors.primary,
            foregroundColor: Colors.white,
            leading: IconButton(
              icon: const Icon(Icons.close),
              onPressed: () => Navigator.of(context).pop(),
            ),
          ),
          body: Padding(
            padding: const EdgeInsets.all(16),
            child: Container(
              padding: const EdgeInsets.all(16),
              child: Center(
                child: Text('مخطط ${widget.item.title}'),
              ),
            ),
          ),
        ),
      ),
    );
  }
}

/// مساعد لبناء مفتاح المخطط
class ChartLegend extends StatelessWidget {
  final List<ChartData> data;
  final bool showValues;

  const ChartLegend({
    super.key,
    required this.data,
    this.showValues = true,
  });

  @override
  Widget build(BuildContext context) {
    return Wrap(
      spacing: 16,
      runSpacing: 8,
      children: data.map((item) {
        return Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 12,
              height: 12,
              decoration: BoxDecoration(
                color: item.color ?? AppColors.primary,
                shape: BoxShape.circle,
              ),
            ),
            const SizedBox(width: 6),
            Text(
              showValues 
                  ? '${item.label} (${item.value.toInt()})'
                  : item.label,
              style: AppStyles.bodySmall,
            ),
          ],
        );
      }).toList(),
    );
  }
}
