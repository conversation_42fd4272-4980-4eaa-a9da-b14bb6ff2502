// مخطط خطي بسيط باستخدام Syncfusion
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:syncfusion_flutter_charts/charts.dart';
import '../../models/dashboard_models.dart';
import '../base_chart_widget.dart';
import '../../../../controllers/task_controller.dart';

/// مخطط خطي بسيط باستخدام Syncfusion
class LineChartWidget extends BaseChartWidget {
  const LineChartWidget({
    super.key,
    required super.item,
    super.filters,
    super.showHeader = true,
    super.showFilters = false,
    super.onRefresh,
  });

  @override
  Widget buildChartContent(BuildContext context, List<ChartData> data) {
    // الحصول على نوع البيانات من config
    final dataType = item.config['dataType'] as String?;

    // إنشاء البيانات حسب النوع
    List<ChartData> chartData;
    switch (dataType) {
      case 'task_timeline':
        chartData = _buildTimelineData();
        break;
      case 'user_performance':
        chartData = _buildUserPerformanceData();
        break;
      default:
        chartData = data.isNotEmpty ? data : _buildDefaultData();
    }

    if (chartData.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.timeline, size: 48, color: Colors.grey),
            SizedBox(height: 16),
            Text('لا توجد بيانات لعرضها'),
          ],
        ),
      );
    }

    return SfCartesianChart(
      primaryXAxis: CategoryAxis(
        labelStyle: const TextStyle(fontSize: 10),
        labelRotation: -45,
      ),
      primaryYAxis: NumericAxis(
        labelStyle: const TextStyle(fontSize: 10),
      ),
      tooltipBehavior: TooltipBehavior(enable: true),
      series: <CartesianSeries<ChartData, String>>[
        LineSeries<ChartData, String>(
          dataSource: chartData,
          xValueMapper: (ChartData data, _) => data.label,
          yValueMapper: (ChartData data, _) => data.value,
          pointColorMapper: (ChartData data, _) => data.color,
          dataLabelSettings: const DataLabelSettings(
            isVisible: true,
            textStyle: TextStyle(fontSize: 10),
          ),
          enableTooltip: true,
          markerSettings: const MarkerSettings(isVisible: true),
        )
      ],
    );
  }

  /// بناء بيانات الجدول الزمني للمهام
  List<ChartData> _buildTimelineData() {
    final taskController = Get.find<TaskController>();
    final tasks = taskController.allTasks;
    final timelineData = <String, int>{};

    // تجميع المهام حسب التاريخ (آخر 30 يوم)
    final now = DateTime.now();
    for (int i = 29; i >= 0; i--) {
      final date = now.subtract(Duration(days: i));
      final dateKey = '${date.day}/${date.month}';
      timelineData[dateKey] = 0;
    }

    for (final task in tasks) {
      if (!task.isDeleted) {
        final taskDate = task.createdAtDateTime;
        final daysDiff = now.difference(taskDate).inDays;
        if (daysDiff >= 0 && daysDiff < 30) {
          final dateKey = '${taskDate.day}/${taskDate.month}';
          timelineData[dateKey] = (timelineData[dateKey] ?? 0) + 1;
        }
      }
    }

    return timelineData.entries.map((entry) => ChartData(
      label: entry.key,
      value: entry.value.toDouble(),
      color: Colors.blue,
    )).toList();
  }

  /// بناء بيانات أداء المستخدمين
  List<ChartData> _buildUserPerformanceData() {
    final taskController = Get.find<TaskController>();
    final tasks = taskController.allTasks;
    final userPerformance = <String, int>{};

    // تجميع المهام المكتملة حسب المستخدم
    for (final task in tasks) {
      if (!task.isDeleted && task.status == 'completed' && task.assigneeId != null) {
        final userId = task.assigneeId.toString();
        userPerformance[userId] = (userPerformance[userId] ?? 0) + 1;
      }
    }

    // أخذ أفضل 5 مستخدمين
    final sortedEntries = userPerformance.entries.toList()
      ..sort((a, b) => b.value.compareTo(a.value));
    final topUsers = sortedEntries.length > 5
        ? sortedEntries.sublist(0, 5)
        : sortedEntries;

    return topUsers.map((entry) => ChartData(
      label: 'مستخدم ${entry.key}',
      value: entry.value.toDouble(),
      color: Colors.orange,
    )).toList();
  }

  /// بناء بيانات افتراضية
  List<ChartData> _buildDefaultData() {
    return [
      ChartData(label: 'يوم 1', value: 10, color: Colors.blue),
      ChartData(label: 'يوم 2', value: 15, color: Colors.blue),
      ChartData(label: 'يوم 3', value: 12, color: Colors.blue),
      ChartData(label: 'يوم 4', value: 18, color: Colors.blue),
      ChartData(label: 'يوم 5', value: 20, color: Colors.blue),
    ];
  }
}
