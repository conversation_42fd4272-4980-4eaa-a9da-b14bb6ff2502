import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../../constants/app_colors.dart';
import '../../../../controllers/task_controller.dart';
import '../../../../controllers/user_controller.dart';
import '../../../../controllers/department_controller.dart';
import '../base_chart_widget.dart';
import '../../models/dashboard_models.dart';

/// Battery Widget - مؤشر التقدم بشكل بطارية لعرض نسب الإكمال
class BatteryWidget extends BaseChartWidget {
  final String? title;
  final String? subtitle;
  final String? dataSource;
  final Map<String, dynamic>? config;

  const BatteryWidget({
    super.key,
    this.title,
    this.subtitle,
    this.dataSource,
    this.config,
    required super.item,
    super.filters,
    super.showHeader,
    super.showFilters,
    super.onRefresh,
  });

  @override
  Widget buildChartContent(BuildContext context, List<ChartData> data) {
    return BatteryContent(
      title: title,
      subtitle: subtitle,
      dataSource: dataSource ?? 'task_completion',
      config: config,
    );
  }
}

/// محتوى Battery Widget المنفصل
class BatteryContent extends StatefulWidget {
  final String? title;
  final String? subtitle;
  final String dataSource;
  final Map<String, dynamic>? config;

  const BatteryContent({
    super.key,
    this.title,
    this.subtitle,
    required this.dataSource,
    this.config,
  });

  @override
  State<BatteryContent> createState() => _BatteryContentState();
}

class _BatteryContentState extends State<BatteryContent>
    with TickerProviderStateMixin {
  
  late final TaskController _taskController;
  late final UserController _userController;
  late final DepartmentController _departmentController;
  
  late AnimationController _animationController;
  late Animation<double> _progressAnimation;
  late Animation<double> _fadeAnimation;
  
  double _progressValue = 0.0;
  String _progressLabel = '';
  Color _batteryColor = Colors.grey;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _initializeControllers();
    _initializeAnimations();
    _loadData();
  }

  void _initializeControllers() {
    _taskController = Get.find<TaskController>();
    _userController = Get.find<UserController>();
    _departmentController = Get.find<DepartmentController>();
  }

  void _initializeAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 2000),
      vsync: this,
    );

    _progressAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOutCubic,
    ));

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  Future<void> _loadData() async {
    setState(() => _isLoading = true);

    try {
      double progress = 0.0;
      String label = '';

      switch (widget.dataSource) {
        case 'task_completion':
          final taskStats = _taskController.taskStats;
          final total = taskStats['total'] ?? 0;
          final completed = taskStats['completed'] ?? 0;
          progress = total > 0 ? completed / total : 0.0;
          label = '${(progress * 100).toInt()}% مكتمل';
          break;

        case 'user_activity':
          final totalUsers = _userController.users.length;
          final activeUsers = _userController.users.where((u) => u.isActive).length;
          progress = totalUsers > 0 ? activeUsers / totalUsers : 0.0;
          label = '${(progress * 100).toInt()}% نشط';
          break;

        case 'department_progress':
          // حساب متوسط تقدم الأقسام بناءً على المهام
          final departments = _departmentController.allDepartments;
          if (departments.isNotEmpty) {
            double totalProgress = 0.0;
            int departmentCount = 0;

            for (final dept in departments) {
              final deptTasks = _taskController.allTasks
                  .where((task) => task.departmentId == dept.id && !task.isDeleted)
                  .toList();
              
              if (deptTasks.isNotEmpty) {
                final completedTasks = deptTasks.where((task) => 
                  task.status == 'completed').length;
                final deptProgress = completedTasks / deptTasks.length;
                totalProgress += deptProgress;
                departmentCount++;
              }
            }

            progress = departmentCount > 0 ? totalProgress / departmentCount : 0.0;
            label = '${(progress * 100).toInt()}% متوسط الأقسام';
          }
          break;

        case 'monthly_target':
          // حساب التقدم نحو الهدف الشهري (افتراضي 100 مهمة)
          final monthlyTarget = widget.config?['target'] ?? 100;
          final currentMonth = DateTime.now().month;
          final currentYear = DateTime.now().year;
          
          final monthlyTasks = _taskController.allTasks.where((task) {
            final taskDate = task.createdAtDateTime;
            return taskDate.month == currentMonth && 
                   taskDate.year == currentYear &&
                   !task.isDeleted;
          }).length;

          progress = monthlyTasks / monthlyTarget;
          if (progress > 1.0) progress = 1.0;
          label = '$monthlyTasks / $monthlyTarget مهمة';
          break;

        default:
          progress = 0.0;
          label = 'غير محدد';
      }

      setState(() {
        _progressValue = progress;
        _progressLabel = label;
        _batteryColor = _getBatteryColor(progress);
        _isLoading = false;
      });

      _animationController.forward();
    } catch (e) {
      debugPrint('خطأ في تحميل بيانات Battery Widget: $e');
      setState(() => _isLoading = false);
    }
  }

  Color _getBatteryColor(double progress) {
    if (progress >= 0.8) return Colors.green;
    if (progress >= 0.6) return Colors.lightGreen;
    if (progress >= 0.4) return Colors.orange;
    if (progress >= 0.2) return Colors.deepOrange;
    return Colors.red;
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildHeader(),
          const SizedBox(height: 20),
          Expanded(
            child: _isLoading ? _buildLoadingState() : _buildBatteryDisplay(),
          ),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: AppColors.primary.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                Icons.battery_charging_full,
                color: AppColors.primary,
                size: 20,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    widget.title ?? 'مؤشر التقدم',
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Colors.black87,
                    ),
                  ),
                  if (widget.subtitle != null) ...[
                    const SizedBox(height: 4),
                    Text(
                      widget.subtitle!,
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey.shade600,
                      ),
                    ),
                  ],
                ],
              ),
            ),
            IconButton(
              icon: const Icon(Icons.refresh, size: 20),
              onPressed: _loadData,
              tooltip: 'تحديث البيانات',
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildLoadingState() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(),
          SizedBox(height: 8),
          Text(
            'جاري تحميل البيانات...',
            style: TextStyle(fontSize: 12, color: Colors.grey),
          ),
        ],
      ),
    );
  }

  Widget _buildBatteryDisplay() {
    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return FadeTransition(
          opacity: _fadeAnimation,
          child: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // البطارية
                _buildBattery(),
                const SizedBox(height: 20),
                // النسبة المئوية
                Text(
                  '${(_progressValue * 100).toInt()}%',
                  style: TextStyle(
                    fontSize: 32,
                    fontWeight: FontWeight.bold,
                    color: _batteryColor,
                  ),
                ),
                const SizedBox(height: 8),
                // التسمية
                Text(
                  _progressLabel,
                  style: const TextStyle(
                    fontSize: 14,
                    color: Colors.black87,
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildBattery() {
    const batteryWidth = 120.0;
    const batteryHeight = 60.0;
    const batteryBorderRadius = 8.0;
    const tipWidth = 8.0;
    const tipHeight = 20.0;

    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        // جسم البطارية
        Container(
          width: batteryWidth,
          height: batteryHeight,
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey.shade400, width: 2),
            borderRadius: BorderRadius.circular(batteryBorderRadius),
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(batteryBorderRadius - 2),
            child: Stack(
              children: [
                // الخلفية
                Container(
                  width: batteryWidth,
                  height: batteryHeight,
                  color: Colors.grey.shade100,
                ),
                // التعبئة المتحركة
                AnimatedBuilder(
                  animation: _progressAnimation,
                  builder: (context, child) {
                    final animatedProgress = _progressAnimation.value * _progressValue;
                    return Container(
                      width: batteryWidth * animatedProgress,
                      height: batteryHeight,
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          colors: [
                            _batteryColor.withOpacity(0.8),
                            _batteryColor,
                          ],
                          begin: Alignment.centerLeft,
                          end: Alignment.centerRight,
                        ),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
        ),
        // طرف البطارية
        Container(
          width: tipWidth,
          height: tipHeight,
          decoration: BoxDecoration(
            color: Colors.grey.shade400,
            borderRadius: const BorderRadius.only(
              topRight: Radius.circular(4),
              bottomRight: Radius.circular(4),
            ),
          ),
        ),
      ],
    );
  }
}
