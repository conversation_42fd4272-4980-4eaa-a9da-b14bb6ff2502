// تم إنشاء ملف جديد بسيط لتجنب تعقيدات fl_chart
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:syncfusion_flutter_charts/charts.dart';
import '../../models/dashboard_models.dart';
import '../base_chart_widget.dart';
import '../../../../controllers/task_controller.dart';
import '../../../../controllers/department_controller.dart';

/// مخطط شريطي بسيط باستخدام Syncfusion
class BarChartWidget extends BaseChartWidget {
  const BarChartWidget({
    super.key,
    required super.item,
    super.filters,
    super.showHeader = true,
    super.showFilters = false,
    super.onRefresh,
  });

  @override
  Widget buildChartContent(BuildContext context, List<ChartData> data) {
    // الحصول على نوع البيانات من config
    final dataType = item.config['dataType'] as String?;

    // إنشاء البيانات حسب النوع
    List<ChartData> chartData;
    switch (dataType) {
      case 'department_distribution':
        chartData = _buildDepartmentData();
        break;
      case 'monthly_tasks':
        chartData = _buildMonthlyData();
        break;
      default:
        chartData = data.isNotEmpty ? data : _buildDefaultData();
    }

    if (chartData.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.bar_chart, size: 48, color: Colors.grey),
            SizedBox(height: 16),
            Text('لا توجد بيانات لعرضها'),
          ],
        ),
      );
    }

    return SfCartesianChart(
      primaryXAxis: CategoryAxis(
        labelStyle: const TextStyle(fontSize: 10),
        labelRotation: -45,
      ),
      primaryYAxis: NumericAxis(
        labelStyle: const TextStyle(fontSize: 10),
      ),
      tooltipBehavior: TooltipBehavior(enable: true),
      series: <CartesianSeries<ChartData, String>>[
        ColumnSeries<ChartData, String>(
          dataSource: chartData,
          xValueMapper: (ChartData data, _) => data.label,
          yValueMapper: (ChartData data, _) => data.value,
          pointColorMapper: (ChartData data, _) => data.color,
          dataLabelSettings: const DataLabelSettings(
            isVisible: true,
            textStyle: TextStyle(fontSize: 10),
          ),
          enableTooltip: true,
        )
      ],
    );
  }

  /// بناء بيانات توزيع الأقسام
  List<ChartData> _buildDepartmentData() {
    final taskController = Get.find<TaskController>();
    final departmentController = Get.find<DepartmentController>();
    final tasks = taskController.allTasks;
    final departments = departmentController.allDepartments;

    final departmentTaskCount = <String, int>{};
    for (final task in tasks) {
      if (!task.isDeleted && task.departmentId != null) {
        final dept = departments.firstWhereOrNull(
          (d) => d.id == task.departmentId,
        );
        if (dept != null) {
          departmentTaskCount[dept.name] = (departmentTaskCount[dept.name] ?? 0) + 1;
        }
      }
    }

    return departmentTaskCount.entries.map((entry) => ChartData(
      label: entry.key,
      value: entry.value.toDouble(),
      color: Colors.teal,
    )).toList();
  }

  /// بناء بيانات المهام الشهرية
  List<ChartData> _buildMonthlyData() {
    final taskController = Get.find<TaskController>();
    final tasks = taskController.allTasks;
    final monthlyData = <String, int>{};

    // تجميع المهام حسب الشهر
    for (final task in tasks) {
      if (!task.isDeleted) {
        final month = '${task.createdAtDateTime.month}/${task.createdAtDateTime.year}';
        monthlyData[month] = (monthlyData[month] ?? 0) + 1;
      }
    }

    // أخذ آخر 6 أشهر
    final sortedEntries = monthlyData.entries.toList()
      ..sort((a, b) => a.key.compareTo(b.key));
    final recentEntries = sortedEntries.length > 6
        ? sortedEntries.sublist(sortedEntries.length - 6)
        : sortedEntries;

    return recentEntries.map((entry) => ChartData(
      label: entry.key,
      value: entry.value.toDouble(),
      color: Colors.blue,
    )).toList();
  }

  /// بناء بيانات افتراضية
  List<ChartData> _buildDefaultData() {
    return [
      ChartData(label: 'عينة 1', value: 30, color: Colors.blue),
      ChartData(label: 'عينة 2', value: 25, color: Colors.green),
      ChartData(label: 'عينة 3', value: 20, color: Colors.orange),
    ];
  }
}
