import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:syncfusion_flutter_charts/charts.dart';
import '../../../../constants/app_colors.dart';
import '../../../../controllers/task_controller.dart';
import '../base_chart_widget.dart';
import '../../models/dashboard_models.dart';

/// Priority Distribution Widget - توزيع المهام حسب الأولوية
class PriorityDistributionWidget extends BaseChartWidget {
  final String? title;
  final String? subtitle;
  final Map<String, dynamic>? config;

  const PriorityDistributionWidget({
    super.key,
    this.title,
    this.subtitle,
    this.config,
    required super.item,
    super.filters,
    super.showHeader,
    super.showFilters,
    super.onRefresh,
  });

  @override
  Widget buildChartContent(BuildContext context, List<ChartData> data) {
    return PriorityDistributionContent(
      title: title,
      subtitle: subtitle,
      config: config,
    );
  }
}

/// محتوى Priority Distribution Widget المنفصل
class PriorityDistributionContent extends StatefulWidget {
  final String? title;
  final String? subtitle;
  final Map<String, dynamic>? config;

  const PriorityDistributionContent({
    super.key,
    this.title,
    this.subtitle,
    this.config,
  });

  @override
  State<PriorityDistributionContent> createState() => _PriorityDistributionContentState();
}

class _PriorityDistributionContentState extends State<PriorityDistributionContent> {
  final TaskController _taskController = Get.find<TaskController>();
  
  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // رأس الـ Widget
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: AppColors.primary.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  Icons.priority_high,
                  color: AppColors.primary,
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      widget.title ?? 'توزيع المهام حسب الأولوية',
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    if (widget.subtitle != null)
                      Text(
                        widget.subtitle!,
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey.shade600,
                        ),
                      ),
                  ],
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 16),
          
          // المخطط الدائري
          Expanded(
            child: _buildPriorityChart(),
          ),
        ],
      ),
    );
  }

  /// بناء مخطط توزيع الأولوية
  Widget _buildPriorityChart() {
    final tasks = _taskController.allTasks;
    final priorityCount = <String, int>{};

    // تجميع المهام حسب الأولوية
    for (final task in tasks) {
      if (!task.isDeleted) {
        priorityCount[task.priority] = (priorityCount[task.priority] ?? 0) + 1;
      }
    }

    if (priorityCount.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.priority_high,
              size: 48,
              color: Colors.grey,
            ),
            SizedBox(height: 16),
            Text(
              'لا توجد مهام لعرضها',
              style: TextStyle(
                color: Colors.grey,
                fontSize: 16,
              ),
            ),
          ],
        ),
      );
    }

    final data = priorityCount.entries.map((entry) =>
      PriorityChartData(entry.key, entry.value.toDouble(), _getPriorityColor(entry.key))
    ).toList();

    return SfCircularChart(
      legend: Legend(
        isVisible: true,
        position: LegendPosition.bottom,
        textStyle: const TextStyle(fontSize: 12),
      ),
      series: <CircularSeries>[
        PieSeries<PriorityChartData, String>(
          dataSource: data,
          xValueMapper: (PriorityChartData data, _) => data.priority,
          yValueMapper: (PriorityChartData data, _) => data.count,
          pointColorMapper: (PriorityChartData data, _) => data.color,
          dataLabelSettings: const DataLabelSettings(
            isVisible: true,
            labelPosition: ChartDataLabelPosition.outside,
            textStyle: TextStyle(fontSize: 10),
          ),
          enableTooltip: true,
        ),
      ],
      tooltipBehavior: TooltipBehavior(enable: true),
    );
  }

  /// الحصول على لون الأولوية
  Color _getPriorityColor(String priority) {
    switch (priority.toLowerCase()) {
      case 'عالية':
      case 'high':
        return Colors.red;
      case 'متوسطة':
      case 'medium':
        return Colors.orange;
      case 'منخفضة':
      case 'low':
        return Colors.green;
      default:
        return Colors.blue;
    }
  }
}

/// نموذج بيانات مخطط الأولوية
class PriorityChartData {
  final String priority;
  final double count;
  final Color color;

  PriorityChartData(this.priority, this.count, this.color);
}
