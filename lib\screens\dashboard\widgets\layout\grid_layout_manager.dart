import 'package:flutter/material.dart';
import '../../models/dashboard_models.dart';

/// مدير التخطيط الشبكي المرن للداشبورد
/// يدعم Drag & Drop و Resize للـ widgets
class GridLayoutManager extends StatefulWidget {
  final List<DashboardItem> items;
  final Function(List<DashboardItem>) onLayoutChanged;
  final Widget Function(DashboardItem) itemBuilder;
  final int gridColumns;
  final double itemHeight;
  final bool enableDragDrop;
  final bool enableResize;

  const GridLayoutManager({
    super.key,
    required this.items,
    required this.onLayoutChanged,
    required this.itemBuilder,
    this.gridColumns = 12,
    this.itemHeight = 200,
    this.enableDragDrop = true,
    this.enableResize = true,
  });

  @override
  State<GridLayoutManager> createState() => _GridLayoutManagerState();
}

class _GridLayoutManagerState extends State<GridLayoutManager> {
  late List<DashboardItem> _items;
  DashboardItem? _draggedItem;
  Offset? _dragOffset;
  
  @override
  void initState() {
    super.initState();
    _items = List.from(widget.items);
  }

  @override
  void didUpdateWidget(GridLayoutManager oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.items != widget.items) {
      _items = List.from(widget.items);
    }
  }

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        final cellWidth = constraints.maxWidth / widget.gridColumns;
        
        return Container(
          width: constraints.maxWidth,
          height: _calculateGridHeight(cellWidth),
          child: Stack(
            children: [
              // شبكة الخلفية (اختيارية للمساعدة البصرية)
              if (widget.enableDragDrop) _buildGridBackground(cellWidth),
              
              // عناصر الداشبورد
              ..._items.map((item) => _buildGridItem(item, cellWidth)),
              
              // عنصر السحب المؤقت
              if (_draggedItem != null && _dragOffset != null)
                _buildDragPreview(_draggedItem!, cellWidth),
            ],
          ),
        );
      },
    );
  }

  /// حساب ارتفاع الشبكة الكلي
  double _calculateGridHeight(double cellWidth) {
    if (_items.isEmpty) return widget.itemHeight;
    
    final maxRow = _items.map((item) => _getItemPosition(item).row + _getItemSize(item).height).reduce((a, b) => a > b ? a : b);
    return maxRow * widget.itemHeight;
  }

  /// بناء خلفية الشبكة للمساعدة البصرية
  Widget _buildGridBackground(double cellWidth) {
    return CustomPaint(
      size: Size.infinite,
      painter: GridPainter(
        cellWidth: cellWidth,
        cellHeight: widget.itemHeight,
        gridColumns: widget.gridColumns,
        gridRows: (_calculateGridHeight(cellWidth) / widget.itemHeight).ceil(),
      ),
    );
  }

  /// بناء عنصر واحد في الشبكة
  Widget _buildGridItem(DashboardItem item, double cellWidth) {
    final position = _getItemPosition(item);
    final size = _getItemSize(item);
    
    final left = position.column * cellWidth;
    final top = position.row * widget.itemHeight;
    final width = size.width * cellWidth;
    final height = size.height * widget.itemHeight;

    Widget child = Container(
      width: width,
      height: height,
      child: widget.itemBuilder(item),
    );

    // إضافة إمكانية السحب
    if (widget.enableDragDrop) {
      child = Draggable<DashboardItem>(
        data: item,
        feedback: _buildDragFeedback(item, width, height),
        childWhenDragging: _buildDragPlaceholder(width, height),
        onDragStarted: () => _onDragStarted(item),
        onDragEnd: (details) => _onDragEnd(item, details, cellWidth),
        child: child,
      );
    }

    // إضافة إمكانية تغيير الحجم
    if (widget.enableResize) {
      child = _buildResizableWidget(child, item, cellWidth);
    }

    return Positioned(
      left: left,
      top: top,
      child: child,
    );
  }

  /// بناء معاينة السحب
  Widget _buildDragPreview(DashboardItem item, double cellWidth) {
    final size = _getItemSize(item);
    final width = size.width * cellWidth;
    final height = size.height * widget.itemHeight;

    return Positioned(
      left: _dragOffset!.dx - (width / 2),
      top: _dragOffset!.dy - (height / 2),
      child: Opacity(
        opacity: 0.7,
        child: Container(
          width: width,
          height: height,
          decoration: BoxDecoration(
            color: Colors.blue.withAlpha(50),
            border: Border.all(color: Colors.blue, width: 2),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Center(
            child: Text(
              item.title,
              style: const TextStyle(
                color: Colors.blue,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ),
      ),
    );
  }

  /// بناء مكان العنصر أثناء السحب
  Widget _buildDragPlaceholder(double width, double height) {
    return Container(
      width: width,
      height: height,
      decoration: BoxDecoration(
        color: Colors.grey.withAlpha(50),
        border: Border.all(color: Colors.grey, width: 2, style: BorderStyle.solid),
        borderRadius: BorderRadius.circular(8),
      ),
      child: const Center(
        child: Icon(
          Icons.drag_indicator,
          color: Colors.grey,
          size: 32,
        ),
      ),
    );
  }

  /// بناء تغذية راجعة للسحب
  Widget _buildDragFeedback(DashboardItem item, double width, double height) {
    return Material(
      elevation: 8,
      borderRadius: BorderRadius.circular(8),
      child: Container(
        width: width,
        height: height,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: Colors.blue, width: 2),
        ),
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                item.chartType.icon,
                color: Colors.blue,
                size: 32,
              ),
              const SizedBox(height: 8),
              Text(
                item.title,
                style: const TextStyle(
                  color: Colors.blue,
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// بناء widget قابل لتغيير الحجم
  Widget _buildResizableWidget(Widget child, DashboardItem item, double cellWidth) {
    return Stack(
      children: [
        child,
        // مقابض تغيير الحجم في الزوايا
        _buildResizeHandle(
          item,
          cellWidth,
          Alignment.bottomRight,
          Icons.drag_handle,
        ),
      ],
    );
  }

  /// بناء مقبض تغيير الحجم
  Widget _buildResizeHandle(
    DashboardItem item,
    double cellWidth,
    Alignment alignment,
    IconData icon,
  ) {
    return Positioned(
      right: alignment == Alignment.bottomRight ? 0 : null,
      bottom: alignment == Alignment.bottomRight ? 0 : null,
      child: GestureDetector(
        onPanUpdate: (details) => _onResizePanUpdate(item, details, cellWidth),
        child: Container(
          width: 20,
          height: 20,
          decoration: BoxDecoration(
            color: Colors.blue,
            borderRadius: BorderRadius.circular(4),
          ),
          child: Icon(
            icon,
            color: Colors.white,
            size: 12,
          ),
        ),
      ),
    );
  }

  /// معالج بداية السحب
  void _onDragStarted(DashboardItem item) {
    setState(() {
      _draggedItem = item;
    });
  }

  /// معالج انتهاء السحب
  void _onDragEnd(DashboardItem item, DraggableDetails details, double cellWidth) {
    final newColumn = (details.offset.dx / cellWidth).round().clamp(0, widget.gridColumns - 1);
    final newRow = (details.offset.dy / widget.itemHeight).round().clamp(0, double.infinity).toInt();

    // تحديث موقع العنصر
    final updatedItem = item.copyWith(
      config: {
        ...item.config,
        'position': {
          'column': newColumn,
          'row': newRow,
        },
      },
    );

    setState(() {
      final index = _items.indexWhere((i) => i.id == item.id);
      if (index != -1) {
        _items[index] = updatedItem;
      }
      _draggedItem = null;
      _dragOffset = null;
    });

    widget.onLayoutChanged(_items);
  }

  /// معالج تحديث تغيير الحجم
  void _onResizePanUpdate(DashboardItem item, DragUpdateDetails details, double cellWidth) {
    final deltaColumns = (details.delta.dx / cellWidth).round();
    final deltaRows = (details.delta.dy / widget.itemHeight).round();

    if (deltaColumns == 0 && deltaRows == 0) return;

    final currentSize = _getItemSize(item);
    final newWidth = (currentSize.width + deltaColumns).clamp(1, widget.gridColumns);
    final newHeight = (currentSize.height + deltaRows).clamp(1, 10);

    final updatedItem = item.copyWith(
      config: {
        ...item.config,
        'size': {
          'width': newWidth,
          'height': newHeight,
        },
      },
    );

    setState(() {
      final index = _items.indexWhere((i) => i.id == item.id);
      if (index != -1) {
        _items[index] = updatedItem;
      }
    });

    widget.onLayoutChanged(_items);
  }

  /// الحصول على موقع العنصر
  GridPosition _getItemPosition(DashboardItem item) {
    final position = item.config['position'] as Map<String, dynamic>?;
    return GridPosition(
      column: position?['column'] ?? 0,
      row: position?['row'] ?? 0,
    );
  }

  /// الحصول على حجم العنصر
  GridSize _getItemSize(DashboardItem item) {
    final size = item.config['size'] as Map<String, dynamic>?;
    return GridSize(
      width: size?['width'] ?? 2,
      height: size?['height'] ?? 1,
    );
  }
}

/// رسام الشبكة للخلفية
class GridPainter extends CustomPainter {
  final double cellWidth;
  final double cellHeight;
  final int gridColumns;
  final int gridRows;

  GridPainter({
    required this.cellWidth,
    required this.cellHeight,
    required this.gridColumns,
    required this.gridRows,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.grey.withAlpha(25)
      ..strokeWidth = 1;

    // رسم الخطوط العمودية
    for (int i = 0; i <= gridColumns; i++) {
      final x = i * cellWidth;
      canvas.drawLine(
        Offset(x, 0),
        Offset(x, gridRows * cellHeight),
        paint,
      );
    }

    // رسم الخطوط الأفقية
    for (int i = 0; i <= gridRows; i++) {
      final y = i * cellHeight;
      canvas.drawLine(
        Offset(0, y),
        Offset(gridColumns * cellWidth, y),
        paint,
      );
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

/// موقع العنصر في الشبكة
class GridPosition {
  final int column;
  final int row;

  const GridPosition({required this.column, required this.row});
}

/// حجم العنصر في الشبكة
class GridSize {
  final int width;
  final int height;

  const GridSize({required this.width, required this.height});
}
