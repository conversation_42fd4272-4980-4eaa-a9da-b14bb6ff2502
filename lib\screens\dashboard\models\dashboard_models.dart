import 'package:flutter/material.dart';

/// أنواع المخططات المدعومة
enum ChartType {
  bar('bar', 'مخطط شريطي', Icons.bar_chart),
  pie('pie', 'مخطط دائري', Icons.pie_chart),
  line('line', 'مخطط خطي', Icons.show_chart),
  gauge('gauge', 'مقياس', Icons.speed),
  kpi('kpi', 'مؤشر أداء', Icons.analytics),
  numbers('numbers', 'أرقام وإحصائيات', Icons.numbers),
  battery('battery', 'مؤشر بطارية', Icons.battery_charging_full),
  timeTracking('time_tracking', 'تتبع الوقت', Icons.access_time),
  workload('workload', 'عبء العمل', Icons.work_outline),
  calendar('calendar', 'تقويم المهام', Icons.calendar_month);

  const ChartType(this.value, this.displayName, this.icon);
  
  final String value;
  final String displayName;
  final IconData icon;
}

/// أنواع الفلاتر الزمنية
enum TimeFilterType {
  today('today', 'اليوم'),
  week('week', 'هذا الأسبوع'),
  month('month', 'هذا الشهر'),
  quarter('quarter', 'هذا الربع'),
  year('year', 'هذا العام'),
  custom('custom', 'مخصص');

  const TimeFilterType(this.value, this.displayName);
  
  final String value;
  final String displayName;
}

/// نموذج عنصر لوحة المعلومات
class DashboardItem {
  final String id;
  final String title;
  final ChartType chartType;
  final Map<String, dynamic> config;
  final bool isVisible;
  final DateTime createdAt;
  final DateTime updatedAt;

  const DashboardItem({
    required this.id,
    required this.title,
    required this.chartType,
    this.config = const {},
    this.isVisible = true,
    required this.createdAt,
    required this.updatedAt,
  });

  /// تحويل إلى JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'chartType': chartType.value,
      'config': config,
      'isVisible': isVisible,
      'createdAt': createdAt.millisecondsSinceEpoch,
      'updatedAt': updatedAt.millisecondsSinceEpoch,
    };
  }

  /// إنشاء من JSON
  factory DashboardItem.fromJson(Map<String, dynamic> json) {
    return DashboardItem(
      id: json['id'],
      title: json['title'],
      chartType: ChartType.values.firstWhere(
        (e) => e.value == json['chartType'],
        orElse: () => ChartType.bar,
      ),
      config: json['config'] ?? {},
      isVisible: json['isVisible'] ?? true,
      createdAt: DateTime.fromMillisecondsSinceEpoch(json['createdAt']),
      updatedAt: DateTime.fromMillisecondsSinceEpoch(json['updatedAt']),
    );
  }

  /// إنشاء نسخة محدثة من العنصر
  DashboardItem copyWith({
    String? id,
    String? title,
    ChartType? chartType,
    Map<String, dynamic>? config,
    bool? isVisible,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return DashboardItem(
      id: id ?? this.id,
      title: title ?? this.title,
      chartType: chartType ?? this.chartType,
      config: config ?? this.config,
      isVisible: isVisible ?? this.isVisible,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}

/// نموذج بيانات المخطط
class ChartData {
  final String label;
  final double value;
  final Color? color;
  final Map<String, dynamic>? metadata;

  const ChartData({
    required this.label,
    required this.value,
    this.color,
    this.metadata,
  });

  Map<String, dynamic> toJson() {
    return {
      'label': label,
      'value': value,
      'color': color?.toARGB32(),
      'metadata': metadata,
    };
  }

  factory ChartData.fromJson(Map<String, dynamic> json) {
    return ChartData(
      label: json['label'],
      value: json['value'].toDouble(),
      color: json['color'] != null ? Color(json['color']) : null,
      metadata: json['metadata'],
    );
  }
}

/// نموذج خيارات الفلتر
class FilterOptions {
  final DateTime? startDate;
  final DateTime? endDate;
  final TimeFilterType filterType;
  final List<String>? departmentIds;
  final List<String>? userIds;
  final List<String>? statusFilter;

  const FilterOptions({
    this.startDate,
    this.endDate,
    this.filterType = TimeFilterType.month,
    this.departmentIds,
    this.userIds,
    this.statusFilter,
  });

  Map<String, dynamic> toJson() {
    return {
      'startDate': startDate?.millisecondsSinceEpoch,
      'endDate': endDate?.millisecondsSinceEpoch,
      'filterType': filterType.value,
      'departmentIds': departmentIds,
      'userIds': userIds,
      'statusFilter': statusFilter,
    };
  }

  factory FilterOptions.fromJson(Map<String, dynamic> json) {
    return FilterOptions(
      startDate: json['startDate'] != null 
          ? DateTime.fromMillisecondsSinceEpoch(json['startDate'])
          : null,
      endDate: json['endDate'] != null 
          ? DateTime.fromMillisecondsSinceEpoch(json['endDate'])
          : null,
      filterType: TimeFilterType.values.firstWhere(
        (e) => e.value == json['filterType'],
        orElse: () => TimeFilterType.month,
      ),
      departmentIds: json['departmentIds']?.cast<String>(),
      userIds: json['userIds']?.cast<String>(),
      statusFilter: json['statusFilter']?.cast<String>(),
    );
  }

  FilterOptions copyWith({
    DateTime? startDate,
    DateTime? endDate,
    TimeFilterType? filterType,
    List<String>? departmentIds,
    List<String>? userIds,
    List<String>? statusFilter,
  }) {
    return FilterOptions(
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      filterType: filterType ?? this.filterType,
      departmentIds: departmentIds ?? this.departmentIds,
      userIds: userIds ?? this.userIds,
      statusFilter: statusFilter ?? this.statusFilter,
    );
  }
}

/// حالات لوحة المعلومات
enum DashboardState {
  loading,
  loaded,
  error,
  empty,
}

/// نموذج حالة لوحة المعلومات
class DashboardStateModel {
  final DashboardState state;
  final List<DashboardItem> items;
  final String? errorMessage;
  final bool isRefreshing;

  const DashboardStateModel({
    required this.state,
    this.items = const [],
    this.errorMessage,
    this.isRefreshing = false,
  });

  DashboardStateModel copyWith({
    DashboardState? state,
    List<DashboardItem>? items,
    String? errorMessage,
    bool? isRefreshing,
  }) {
    return DashboardStateModel(
      state: state ?? this.state,
      items: items ?? this.items,
      errorMessage: errorMessage ?? this.errorMessage,
      isRefreshing: isRefreshing ?? this.isRefreshing,
    );
  }
}
