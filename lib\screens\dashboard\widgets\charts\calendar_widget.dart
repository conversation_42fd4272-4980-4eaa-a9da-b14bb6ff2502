import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../../controllers/task_controller.dart';
import '../../../../models/task_models.dart';
import '../../../../constants/app_colors.dart';
import '../../../../constants/app_styles.dart';
import '../base_chart_widget.dart';
import '../../models/dashboard_models.dart';

/// ويدجت التقويم لعرض المهام حسب التاريخ
/// يعرض المهام في تقويم تفاعلي مع إمكانية النقر على الأيام
class CalendarWidget extends BaseChartWidget {
  final String? title;
  final String? subtitle;
  final Map<String, dynamic>? config;

  const CalendarWidget({
    super.key,
    this.title,
    this.subtitle,
    this.config,
    required super.item,
    super.filters,
    super.showHeader,
    super.showFilters,
    super.onRefresh,
  });

  @override
  Widget buildChartContent(BuildContext context, List<ChartData> data) {
    return CalendarContent(
      title: title,
      subtitle: subtitle,
      config: config,
    );
  }
}

/// محتوى التقويم المنفصل
class CalendarContent extends StatefulWidget {
  final String? title;
  final String? subtitle;
  final Map<String, dynamic>? config;

  const CalendarContent({
    super.key,
    this.title,
    this.subtitle,
    this.config,
  });

  @override
  State<CalendarContent> createState() => _CalendarContentState();
}

class _CalendarContentState extends State<CalendarContent> {
  final TaskController _taskController = Get.find<TaskController>();

  // متغيرات التقويم
  DateTime _selectedDate = DateTime.now();
  DateTime _focusedDate = DateTime.now();
  Map<DateTime, List<Task>> _tasksByDate = {};
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _loadTasksForCalendar();

    // الاستماع لتغييرات المهام للتحديث التلقائي
    _taskController.addListener(_onTasksChanged);
  }

  @override
  void dispose() {
    _taskController.removeListener(_onTasksChanged);
    super.dispose();
  }

  /// معالج تغيير المهام للتحديث التلقائي
  void _onTasksChanged() {
    if (mounted) {
      _loadTasksForCalendar();
    }
  }

  /// تحميل المهام وتجميعها حسب التاريخ
  Future<void> _loadTasksForCalendar() async {
    if (!mounted) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final tasks = _taskController.allTasks;
      final Map<DateTime, List<Task>> tasksByDate = {};

      for (final task in tasks) {
        if (task.dueDate != null && !task.isDeleted) {
          // تحويل Unix timestamp إلى DateTime وإزالة الوقت (الاحتفاظ بالتاريخ فقط)
          final taskDate = DateTime.fromMillisecondsSinceEpoch(task.dueDate! * 1000);
          final dateOnly = DateTime(taskDate.year, taskDate.month, taskDate.day);

          if (tasksByDate[dateOnly] == null) {
            tasksByDate[dateOnly] = [];
          }
          tasksByDate[dateOnly]!.add(task);
        }
      }

      if (mounted) {
        setState(() {
          _tasksByDate = tasksByDate;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    // إظهار مؤشر التحميل إذا كانت البيانات تحمل
    if (_isLoading) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text('جاري تحميل المهام...'),
          ],
        ),
      );
    }

    return Column(
      children: [
        // رأس التقويم مع أزرار التنقل
        _buildCalendarHeader(),
        const SizedBox(height: 16),

        // التقويم الرئيسي
        Expanded(
          child: _buildCalendarGrid(),
        ),

        const SizedBox(height: 16),

        // قائمة المهام لليوم المحدد
        _buildSelectedDayTasks(),
      ],
    );
  }

  /// بناء رأس التقويم مع أزرار التنقل
  Widget _buildCalendarHeader() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: AppColors.primary.withAlpha(25),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          // زر الشهر السابق
          IconButton(
            onPressed: () {
              setState(() {
                _focusedDate = DateTime(_focusedDate.year, _focusedDate.month - 1);
              });
            },
            icon: const Icon(Icons.chevron_left),
            tooltip: 'الشهر السابق',
          ),
          
          // عرض الشهر والسنة الحالية
          Text(
            _getMonthYearText(_focusedDate),
            style: AppStyles.titleMedium.copyWith(
              fontWeight: FontWeight.bold,
              color: AppColors.primary,
            ),
          ),
          
          // زر الشهر التالي
          IconButton(
            onPressed: () {
              setState(() {
                _focusedDate = DateTime(_focusedDate.year, _focusedDate.month + 1);
              });
            },
            icon: const Icon(Icons.chevron_right),
            tooltip: 'الشهر التالي',
          ),
        ],
      ),
    );
  }

  /// بناء شبكة التقويم
  Widget _buildCalendarGrid() {
    return Column(
      children: [
        // أسماء أيام الأسبوع
        _buildWeekdayHeaders(),
        const SizedBox(height: 8),
        
        // أيام الشهر
        Expanded(
          child: _buildMonthDays(),
        ),
      ],
    );
  }

  /// بناء رؤوس أيام الأسبوع
  Widget _buildWeekdayHeaders() {
    const weekdays = ['أحد', 'اثنين', 'ثلاثاء', 'أربعاء', 'خميس', 'جمعة', 'سبت'];
    
    return Row(
      children: weekdays.map((day) => Expanded(
        child: Container(
          padding: const EdgeInsets.symmetric(vertical: 8),
          child: Text(
            day,
            textAlign: TextAlign.center,
            style: AppStyles.bodySmall.copyWith(
              fontWeight: FontWeight.bold,
              color: AppColors.textSecondary,
            ),
          ),
        ),
      )).toList(),
    );
  }

  /// بناء أيام الشهر
  Widget _buildMonthDays() {
    final firstDayOfMonth = DateTime(_focusedDate.year, _focusedDate.month, 1);
    final lastDayOfMonth = DateTime(_focusedDate.year, _focusedDate.month + 1, 0);
    
    // حساب اليوم الأول من الأسبوع (الأحد = 0)
    final firstWeekday = firstDayOfMonth.weekday % 7;
    
    // حساب عدد الأيام المطلوبة لعرضها
    final daysInMonth = lastDayOfMonth.day;
    final totalCells = ((daysInMonth + firstWeekday - 1) / 7).ceil() * 7;
    
    return GridView.builder(
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 7,
        childAspectRatio: 1.0,
      ),
      itemCount: totalCells,
      itemBuilder: (context, index) {
        final dayNumber = index - firstWeekday + 1;
        
        if (dayNumber < 1 || dayNumber > daysInMonth) {
          // خلايا فارغة قبل وبعد أيام الشهر
          return const SizedBox();
        }
        
        final currentDate = DateTime(_focusedDate.year, _focusedDate.month, dayNumber);
        final tasksForDay = _tasksByDate[currentDate] ?? [];
        final isSelected = _isSameDay(currentDate, _selectedDate);
        final isToday = _isSameDay(currentDate, DateTime.now());
        
        return _buildDayCell(currentDate, tasksForDay, isSelected, isToday);
      },
    );
  }

  /// بناء خلية اليوم الواحد
  Widget _buildDayCell(DateTime date, List<Task> tasks, bool isSelected, bool isToday) {
    return GestureDetector(
      onTap: () {
        setState(() {
          _selectedDate = date;
        });
      },
      child: Container(
        margin: const EdgeInsets.all(2),
        decoration: BoxDecoration(
          color: isSelected 
              ? AppColors.primary 
              : isToday 
                  ? AppColors.primary.withAlpha(50)
                  : Colors.transparent,
          borderRadius: BorderRadius.circular(8),
          border: isToday && !isSelected 
              ? Border.all(color: AppColors.primary, width: 2)
              : null,
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // رقم اليوم
            Text(
              date.day.toString(),
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.bold,
                color: isSelected 
                    ? Colors.white 
                    : isToday 
                        ? AppColors.primary
                        : AppColors.textPrimary,
              ),
            ),
            
            // مؤشر المهام
            if (tasks.isNotEmpty) ...[
              const SizedBox(height: 2),
              Container(
                width: 6,
                height: 6,
                decoration: BoxDecoration(
                  color: isSelected ? Colors.white : _getTasksIndicatorColor(tasks),
                  shape: BoxShape.circle,
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  /// بناء قائمة المهام لليوم المحدد
  Widget _buildSelectedDayTasks() {
    final tasksForSelectedDay = _tasksByDate[_selectedDate] ?? [];
    
    if (tasksForSelectedDay.isEmpty) {
      return Container(
        padding: const EdgeInsets.all(16),
        child: Text(
          'لا توجد مهام في ${_getDateText(_selectedDate)}',
          style: AppStyles.bodyMedium.copyWith(
            color: AppColors.textSecondary,
          ),
          textAlign: TextAlign.center,
        ),
      );
    }
    
    return SizedBox(
      height: 120,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'مهام ${_getDateText(_selectedDate)} (${tasksForSelectedDay.length})',
            style: AppStyles.titleSmall.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Expanded(
            child: ListView.builder(
              itemCount: tasksForSelectedDay.length,
              itemBuilder: (context, index) {
                final task = tasksForSelectedDay[index];
                return _buildTaskItem(task);
              },
            ),
          ),
        ],
      ),
    );
  }

  /// بناء عنصر المهمة مع إمكانية النقر للتفاصيل
  Widget _buildTaskItem(Task task) {
    return GestureDetector(
      onTap: () => _showTaskDetails(task),
      child: Container(
        margin: const EdgeInsets.only(bottom: 4),
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        decoration: BoxDecoration(
          color: _getTaskStatusColor(task.status).withAlpha(25),
          borderRadius: BorderRadius.circular(4),
          border: Border.all(
            color: _getTaskStatusColor(task.status),
            width: 1,
          ),
        ),
        child: Row(
          children: [
            // مؤشر الأولوية
            Container(
              width: 4,
              height: 16,
              decoration: BoxDecoration(
                color: _getTaskPriorityColor(task.priority),
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            const SizedBox(width: 8),

            // عنوان المهمة
            Expanded(
              child: Text(
                task.title,
                style: AppStyles.bodySmall,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ),

            // نسبة الإكمال
            Text(
              '${task.completionPercentage}%',
              style: AppStyles.bodySmall.copyWith(
                color: AppColors.textSecondary,
                fontSize: 10,
              ),
            ),

            // أيقونة للإشارة إلى إمكانية النقر
            const SizedBox(width: 4),
            Icon(
              Icons.arrow_forward_ios,
              size: 12,
              color: AppColors.textSecondary,
            ),
          ],
        ),
      ),
    );
  }

  /// إظهار تفاصيل المهمة في dialog
  void _showTaskDetails(Task task) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return Dialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          child: Container(
            width: MediaQuery.of(context).size.width * 0.8,
            constraints: const BoxConstraints(
              maxWidth: 500,
              maxHeight: 600,
            ),
            padding: const EdgeInsets.all(20),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // رأس المهمة
                Row(
                  children: [
                    Container(
                      width: 8,
                      height: 8,
                      decoration: BoxDecoration(
                        color: _getTaskPriorityColor(task.priority),
                        shape: BoxShape.circle,
                      ),
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        task.title,
                        style: AppStyles.titleMedium.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                    IconButton(
                      onPressed: () => Navigator.of(context).pop(),
                      icon: const Icon(Icons.close),
                    ),
                  ],
                ),

                const SizedBox(height: 16),

                // معلومات المهمة
                _buildTaskDetailRow('الحالة', task.status, _getTaskStatusColor(task.status)),
                _buildTaskDetailRow('الأولوية', task.priority, _getTaskPriorityColor(task.priority)),
                _buildTaskDetailRow('نسبة الإكمال', '${task.completionPercentage}%', AppColors.primary),

                if (task.dueDate != null)
                  _buildTaskDetailRow(
                    'تاريخ الاستحقاق',
                    _getDateText(DateTime.fromMillisecondsSinceEpoch(task.dueDate! * 1000)),
                    AppColors.textSecondary,
                  ),

                if (task.description != null && task.description!.isNotEmpty) ...[
                  const SizedBox(height: 16),
                  Text(
                    'الوصف:',
                    style: AppStyles.titleSmall.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: Colors.grey.shade100,
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Text(
                      task.description!,
                      style: AppStyles.bodyMedium,
                    ),
                  ),
                ],

                const SizedBox(height: 20),

                // أزرار الإجراءات
                Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    TextButton(
                      onPressed: () => Navigator.of(context).pop(),
                      child: const Text('إغلاق'),
                    ),
                    const SizedBox(width: 8),
                    ElevatedButton(
                      onPressed: () {
                        Navigator.of(context).pop();
                        // يمكن إضافة التنقل إلى صفحة تفاصيل المهمة هنا
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppColors.primary,
                        foregroundColor: Colors.white,
                      ),
                      child: const Text('عرض التفاصيل'),
                    ),
                  ],
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  /// بناء صف تفاصيل المهمة
  Widget _buildTaskDetailRow(String label, String value, Color color) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        children: [
          SizedBox(
            width: 100,
            child: Text(
              '$label:',
              style: AppStyles.bodyMedium.copyWith(
                fontWeight: FontWeight.w500,
                color: AppColors.textSecondary,
              ),
            ),
          ),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: color.withAlpha(25),
              borderRadius: BorderRadius.circular(4),
              border: Border.all(color: color, width: 1),
            ),
            child: Text(
              value,
              style: AppStyles.bodyMedium.copyWith(
                color: color,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// الحصول على نص الشهر والسنة
  String _getMonthYearText(DateTime date) {
    const months = [
      'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
      'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
    ];
    return '${months[date.month - 1]} ${date.year}';
  }

  /// الحصول على نص التاريخ
  String _getDateText(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  /// التحقق من تطابق التاريخين (بدون الوقت)
  bool _isSameDay(DateTime date1, DateTime date2) {
    return date1.year == date2.year && 
           date1.month == date2.month && 
           date1.day == date2.day;
  }

  /// الحصول على لون مؤشر المهام
  Color _getTasksIndicatorColor(List<Task> tasks) {
    // إذا كان هناك مهام متأخرة، أحمر
    if (tasks.any((task) => task.daysRemaining != null && task.daysRemaining! < 0)) {
      return Colors.red;
    }
    // إذا كان هناك مهام مكتملة، أخضر
    if (tasks.any((task) => task.isCompleted)) {
      return Colors.green;
    }
    // مهام عادية، أزرق
    return AppColors.primary;
  }

  /// الحصول على لون حالة المهمة
  Color _getTaskStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'completed':
      case 'مكتملة':
        return Colors.green;
      case 'in_progress':
      case 'قيد التنفيذ':
        return Colors.blue;
      case 'pending':
      case 'معلقة':
        return Colors.orange;
      default:
        return Colors.grey;
    }
  }

  /// الحصول على لون أولوية المهمة
  Color _getTaskPriorityColor(String priority) {
    switch (priority.toLowerCase()) {
      case 'high':
      case 'عالية':
        return Colors.red;
      case 'medium':
      case 'متوسطة':
        return Colors.orange;
      case 'low':
      case 'منخفضة':
        return Colors.green;
      default:
        return Colors.grey;
    }
  }
}
