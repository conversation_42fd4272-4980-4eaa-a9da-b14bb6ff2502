// // ملف معطل مؤقتاً لحل مشاكل البناء
// import 'package:flutter/material.dart';
// import 'package:intl/intl.dart';


// import '../../models/task_model.dart';

// import '../../models/chart_enums.dart' as chart_enums;
// import '../../models/advanced_filter_options.dart';


// // Define a GanttTask class for backward compatibility
// class GanttTask {
//   final String id;
//   final String title;
//   final DateTime startDate;
//   final DateTime endDate;
//   final double completionPercentage;
//   final Color? color;
//   final String? parentId;
//   final Map<String, dynamic>? additionalData;

//   const GanttTask({
//     required this.id,
//     required this.title,
//     required this.startDate,
//     required this.endDate,
//     this.completionPercentage = 0,
//     this.color,
//     this.parentId,
//     this.additionalData,
//   });

//   // Convert GanttTask to Task
//   Task toTask() {
//     return Task(
//       id: int.tryParse(id) ?? 0,
//       title: title,
//       description: additionalData?['description'] ?? '',
//       creatorId: additionalData?['creatorId'] ?? 1,
//       departmentId: additionalData?['departmentId'] ?? 1,
//       accessUserIds: const [],
//       createdAt: startDate.millisecondsSinceEpoch ~/ 1000,
//       startDate: startDate.millisecondsSinceEpoch ~/ 1000,
//       dueDate: endDate.millisecondsSinceEpoch ~/ 1000,
//       completionPercentage: completionPercentage.toInt(),
//       status: completionPercentage >= 100 ? 'completed' : 'in_progress',
//       priority: 'medium',
//     );
//   }
// }

// /// شاشة الرسوم البيانية المحسنة
// ///
// /// توفر واجهة لعرض الرسوم البيانية المختلفة للتقارير
// class EnhancedChartsScreen extends StatefulWidget {
//   const EnhancedChartsScreen({super.key});

//   @override
//   State<EnhancedChartsScreen> createState() => _EnhancedChartsScreenState();
// }

// class _EnhancedChartsScreenState extends State<EnhancedChartsScreen>
//     with SingleTickerProviderStateMixin {
//   late TabController _tabController;
//   bool _isLoading = true;
//   String? _errorMessage;

//   // بيانات الرسوم البيانية
//   Map<String, double> _taskStatusData = {};
//   Map<String, double> _userPerformanceData = {};
//   Map<String, double> _departmentPerformanceData = {};
//   Map<String, Map<String, double>> _taskProgressData = {};
//   List<GanttTask> _ganttTasks = [];

//   @override
//   void initState() {
//     super.initState();
//     _tabController = TabController(length: 5, vsync: this);
//     _loadChartData();
//   }

//   @override
//   void dispose() {
//     _tabController.dispose();
//     super.dispose();
//   }

//   /// تحميل بيانات الرسوم البيانية
//   Future<void> _loadChartData() async {
//     setState(() {
//       _isLoading = true;
//       _errorMessage = null;
//     });

//     try {
//       // تحميل بيانات حالة المهام
//       await _loadTaskStatusData();

//       // تحميل بيانات أداء المستخدمين
//       await _loadUserPerformanceData();

//       // تحميل بيانات أداء الأقسام
//       await _loadDepartmentPerformanceData();

//       // تحميل بيانات تقدم المهام
//       await _loadTaskProgressData();

//       // تحميل بيانات مخطط Gantt
//       await _loadGanttData();

//       setState(() {
//         _isLoading = false;
//       });
//     } catch (e) {
//       setState(() {
//         _isLoading = false;
//         _errorMessage = 'حدث خطأ أثناء تحميل البيانات: $e';
//       });
//     }
//   }

//   /// تحميل بيانات حالة المهام
//   Future<void> _loadTaskStatusData() async {
//     // هنا يمكن استخدام خدمة التقارير لجلب البيانات الفعلية
//     // لأغراض العرض، سنستخدم بيانات تجريبية

//     setState(() {
//       _taskStatusData = {
//         'مكتملة': 45,
//         'قيد التنفيذ': 30,
//         'معلقة': 15,
//         'متوقفة': 7,
//         'ملغاة': 3,
//       };
//     });
//   }

//   /// تحميل بيانات أداء المستخدمين
//   Future<void> _loadUserPerformanceData() async {
//     // هنا يمكن استخدام خدمة التقارير لجلب البيانات الفعلية
//     // لأغراض العرض، سنستخدم بيانات تجريبية

//     setState(() {
//       _userPerformanceData = {
//         'أحمد محمد': 85,
//         'سارة علي': 72,
//         'محمد خالد': 65,
//         'فاطمة أحمد': 90,
//         'عمر حسن': 78,
//       };
//     });
//   }

//   /// تحميل بيانات أداء الأقسام
//   Future<void> _loadDepartmentPerformanceData() async {
//     // هنا يمكن استخدام خدمة التقارير لجلب البيانات الفعلية
//     // لأغراض العرض، سنستخدم بيانات تجريبية

//     setState(() {
//       _departmentPerformanceData = {
//         'تطوير البرمجيات': 82,
//         'التسويق': 75,
//         'الموارد البشرية': 68,
//         'المبيعات': 90,
//         'خدمة العملاء': 85,
//       };
//     });
//   }

//   /// تحميل بيانات تقدم المهام
//   Future<void> _loadTaskProgressData() async {
//     // هنا يمكن استخدام خدمة التقارير لجلب البيانات الفعلية
//     // لأغراض العرض، سنستخدم بيانات تجريبية

//     // إنشاء بيانات لمدة 30 يوم
//     final now = DateTime.now();
//     final startDate = now.subtract(const Duration(days: 30));

//     final Map<String, double> projectAProgress = {};
//     final Map<String, double> projectBProgress = {};
//     final Map<String, double> projectCProgress = {};

//     for (int i = 0; i < 30; i++) {
//       final day = startDate.add(Duration(days: i));
//       final dayKey = DateFormat('MM/dd').format(day);

//       // محاكاة تقدم المشاريع
//       final projectAValue = 10 + (i * 3.0);
//       final projectBValue = 5 + (i * 2.5);
//       final projectCValue = 15 + (i * 2.0);

//       projectAProgress[dayKey] = projectAValue > 100 ? 100 : projectAValue;
//       projectBProgress[dayKey] = projectBValue > 100 ? 100 : projectBValue;
//       projectCProgress[dayKey] = projectCValue > 100 ? 100 : projectCValue;
//     }

//     setState(() {
//       _taskProgressData = {
//         'مشروع أ': projectAProgress,
//         'مشروع ب': projectBProgress,
//         'مشروع ج': projectCProgress,
//       };
//     });
//   }

//   /// تحميل بيانات مخطط Gantt
//   Future<void> _loadGanttData() async {
//     // هنا يمكن استخدام خدمة التقارير لجلب البيانات الفعلية
//     // لأغراض العرض، سنستخدم بيانات تجريبية

//     final now = DateTime.now();
//     final List<GanttTask> tasks = [
//       GanttTask(
//         id: '1',
//         title: 'تحليل المتطلبات',
//         startDate: now.subtract(const Duration(days: 10)),
//         endDate: now.subtract(const Duration(days: 5)),
//         completionPercentage: 100,
//         color: Colors.blue,
//       ),
//       GanttTask(
//         id: '2',
//         title: 'تصميم واجهة المستخدم',
//         startDate: now.subtract(const Duration(days: 7)),
//         endDate: now.add(const Duration(days: 3)),
//         completionPercentage: 70,
//         color: Colors.green,
//       ),
//       GanttTask(
//         id: '3',
//         title: 'تطوير الواجهة الأمامية',
//         startDate: now.subtract(const Duration(days: 3)),
//         endDate: now.add(const Duration(days: 7)),
//         completionPercentage: 40,
//         color: Colors.orange,
//       ),
//       GanttTask(
//         id: '4',
//         title: 'تطوير الواجهة الخلفية',
//         startDate: now.add(const Duration(days: 2)),
//         endDate: now.add(const Duration(days: 12)),
//         completionPercentage: 10,
//         color: Colors.purple,
//       ),
//       GanttTask(
//         id: '5',
//         title: 'اختبار النظام',
//         startDate: now.add(const Duration(days: 10)),
//         endDate: now.add(const Duration(days: 15)),
//         completionPercentage: 0,
//         color: Colors.red,
//       ),
//     ];

//     setState(() {
//       _ganttTasks = tasks;
//     });
//   }

//   @override
//   Widget build(BuildContext context) {
//     return Scaffold(
//       appBar: AppBar(
//         title: const Text('الرسوم البيانية المحسنة'),
//         actions: [
//           IconButton(
//             icon: const Icon(Icons.refresh),
//             onPressed: _loadChartData,
//             tooltip: 'تحديث البيانات',
//           ),
//         ],
//         bottom: TabBar(
//           controller: _tabController,
//           isScrollable: true,
//           tabs: const [
//             Tab(text: 'حالة المهام'),
//             Tab(text: 'أداء المستخدمين'),
//             Tab(text: 'أداء الأقسام'),
//             Tab(text: 'تقدم المهام'),
//             Tab(text: 'مخطط Gantt'),
//           ],
//         ),
//       ),
//       body: _isLoading
//           ? const Center(child: CircularProgressIndicator())
//           : _errorMessage != null
//               ? Center(
//                   child: Text(_errorMessage!,
//                       style: const TextStyle(color: Colors.red)))
//               : TabBarView(
//                   controller: _tabController,
//                   children: [
//                     _buildTaskStatusChart(),
//                     _buildUserPerformanceChart(),
//                     _buildDepartmentPerformanceChart(),
//                     _buildTaskProgressChart(),
//                     _buildGanttChart(),
//                   ],
//                 ),
//     );
//   }

//   /// بناء رسم بياني لحالة المهام
//   Widget _buildTaskStatusChart() {
//     return Padding(
//       padding: const EdgeInsets.all(16.0),
//       child: Column(
//         children: [
//           const Text(
//             'توزيع حالة المهام',
//             style: TextStyle(
//               fontSize: 18,
//               fontWeight: FontWeight.bold,
//             ),
//           ),
//           const SizedBox(height: 16),
//           Expanded(
//             child: Container(
//               padding: const EdgeInsets.all(16),
//               child: const Center(child: Text('مخطط دائري - قيد التطوير')),
//             ),
//           ),
//           const SizedBox(height: 16),
//           ElevatedButton.icon(
//             onPressed: () {
//               // هنا يمكن إضافة وظيفة تصدير التقرير
//               _exportChart('task_status');
//             },
//             icon: const Icon(Icons.download),
//             label: const Text('تصدير التقرير'),
//           ),
//         ],
//       ),
//     );
//   }

//   /// بناء رسم بياني لأداء المستخدمين
//   Widget _buildUserPerformanceChart() {
//     return Padding(
//       padding: const EdgeInsets.all(16.0),
//       child: Column(
//         children: [
//           const Text(
//             'أداء المستخدمين',
//             style: TextStyle(
//               fontSize: 18,
//               fontWeight: FontWeight.bold,
//             ),
//           ),
//           const SizedBox(height: 16),
//           Expanded(
//             child: Container(
//               padding: const EdgeInsets.all(16),
//               child: const Center(child: Text('مخطط شريطي - قيد التطوير')),
//             ),
//           ),
//           const SizedBox(height: 16),
//           ElevatedButton.icon(
//             onPressed: () {
//               // هنا يمكن إضافة وظيفة تصدير التقرير
//               _exportChart('user_performance');
//             },
//             icon: const Icon(Icons.download),
//             label: const Text('تصدير التقرير'),
//           ),
//         ],
//       ),
//     );
//   }

//   /// بناء رسم بياني لأداء الأقسام
//   Widget _buildDepartmentPerformanceChart() {
//     return Padding(
//       padding: const EdgeInsets.all(16.0),
//       child: Column(
//         children: [
//           const Text(
//             'أداء الأقسام',
//             style: TextStyle(
//               fontSize: 18,
//               fontWeight: FontWeight.bold,
//             ),
//           ),
//           const SizedBox(height: 16),
//           Expanded(
//             child: EnhancedBarChart(
//               data: _departmentPerformanceData,
//               title: 'نسبة إنجاز المهام حسب القسم',
//               xAxisTitle: 'القسم',
//               yAxisTitle: 'نسبة الإنجاز',
//               showGrid: true,

//               maxY: 100,
//               barColors: {
//                 'تطوير البرمجيات': Colors.blue,
//                 'التسويق': Colors.green,
//                 'الموارد البشرية': Colors.orange,
//                 'المبيعات': Colors.purple,
//                 'خدمة العملاء': Colors.teal,
//               },
//               chartType: chart_enums.ChartType.bar,
//               advancedFilterOptions: const AdvancedFilterOptions(),
//                showValues: true,
//             ),
//           ),
//           const SizedBox(height: 16),
//           ElevatedButton.icon(
//             onPressed: () {
//               // هنا يمكن إضافة وظيفة تصدير التقرير
//               _exportChart('department_performance');
//             },
//             icon: const Icon(Icons.download),
//             label: const Text('تصدير التقرير'),
//           ),
//         ],
//       ),
//     );
//   }

//   /// بناء رسم بياني لتقدم المهام
//   Widget _buildTaskProgressChart() {
//     return Padding(
//       padding: const EdgeInsets.all(16.0),
//       child: Column(
//         children: [
//           const Text(
//             'تقدم المهام',
//             style: TextStyle(
//               fontSize: 18,
//               fontWeight: FontWeight.bold,
//             ),
//           ),
//           const SizedBox(height: 16),
//           Expanded(
//             child: EnhancedLineChart(
//               data: _taskProgressData,
//               title: 'تقدم المهام على مدار الوقت',
//               xAxisTitle: 'التاريخ',
//               yAxisTitle: 'نسبة الإنجاز',
//               showGrid: true,
//               showDots: true,
//               showBelowArea: true,
//               formatXAsDate: true,
//               dateFormat: 'MM/dd',
//               lineColors: {
//                 'مشروع أ': Colors.blue,
//                 'مشروع ب': Colors.green,
//                 'مشروع ج': Colors.orange,
//               },
//               chartType: chart_enums.ChartType.line,
//               advancedFilterOptions: const AdvancedFilterOptions(),
//             ),
//           ),
//           const SizedBox(height: 16),
//           ElevatedButton.icon(
//             onPressed: () {
//               // هنا يمكن إضافة وظيفة تصدير التقرير
//               _exportChart('task_progress');
//             },
//             icon: const Icon(Icons.download),
//             label: const Text('تصدير التقرير'),
//           ),
//         ],
//       ),
//     );
//   }

//   /// بناء مخطط Gantt
//   Widget _buildGanttChart() {
//     // تحويل GanttTask إلى Task
//     final List<Task> tasks =
//         _ganttTasks.map((ganttTask) => ganttTask.toTask()).toList();

//     return Padding(
//       padding: const EdgeInsets.all(16.0),
//       child: Column(
//         children: [
//           const Text(
//             'مخطط Gantt للمهام',
//             style: TextStyle(
//               fontSize: 18,
//               fontWeight: FontWeight.bold,
//             ),
//           ),
//           const SizedBox(height: 16),
//           Expanded(
//             child: EnhancedGanttChart(
//               tasks: tasks,
//               title: 'جدول المهام',
//               taskBarHeight: 30,
//               taskBarSpacing: 12,
//               taskNameWidth: 200,
//               viewRange: GanttViewRange.month,
//               onTaskTap: (task) {
//                 // هنا يمكن إضافة وظيفة عرض تفاصيل المهمة
//                 _showTaskDetailsFromTask(task);
//               },
//             ),
//           ),
//           const SizedBox(height: 16),
//           ElevatedButton.icon(
//             onPressed: () {
//               // هنا يمكن إضافة وظيفة تصدير التقرير
//               _exportChart('gantt_chart');
//             },
//             icon: const Icon(Icons.download),
//             label: const Text('تصدير التقرير'),
//           ),
//         ],
//       ),
//     );
//   }

//   /// عرض تفاصيل المهمة من كائن Task
//   void _showTaskDetailsFromTask(Task task) {
//     showDialog(
//       context: context,
//       builder: (context) => AlertDialog(
//         title: Text(task.title),
//         content: Column(
//           mainAxisSize: MainAxisSize.min,
//           crossAxisAlignment: CrossAxisAlignment.start,
//           children: [
//             Text(
//                 'تاريخ البداية: ${task.startDateDateTime != null ? DateFormat('yyyy-MM-dd').format(task.startDateDateTime!) : 'غير محدد'}'),
//             Text(
//                 'تاريخ الاستحقاق: ${task.dueDateDateTime != null ? DateFormat('yyyy-MM-dd').format(task.dueDateDateTime!) : 'غير محدد'}'),
//             Text('نسبة الإنجاز: ${task.completionPercentage}%'),
//             const SizedBox(height: 8),
//             LinearProgressIndicator(
//               value: task.completionPercentage / 100,
//               backgroundColor: Colors.grey[300],
//               valueColor: AlwaysStoppedAnimation<Color>(_getTaskColor(task)),
//             ),
//             const SizedBox(height: 8),
//             Text('الحالة: ${_getTaskStatusText(task.status)}'),
//             Text('الأولوية: ${_getTaskPriorityText(task.priority)}'),
//           ],
//         ),
//         actions: [
//           TextButton(
//             onPressed: () => Navigator.of(context).pop(),
//             child: const Text('إغلاق'),
//           ),
//         ],
//       ),
//     );
//   }

//   /// الحصول على لون المهمة حسب حالتها
//   Color _getTaskColor(Task task) {
//     switch (task.status) {
//       case 'completed':
//         return Colors.blue.shade600;
//       case 'in_progress':
//         return Colors.green;
//       case 'waiting_for_info':
//         return Colors.orange;
//       case 'pending':
//         return Colors.amber;
//       case 'cancelled':
//         return Colors.red;
//       case 'news':
//         return Colors.indigo;
//       default:
//         return Colors.grey;
//     }
//   }

//   /// الحصول على نص حالة المهمة
//   String _getTaskStatusText(String status) {
//     switch (status) {
//       case 'completed': // completed
//         return 'مكتملة';
//       case 'inProgress': // inProgress
//         return 'قيد التنفيذ';
//       case 'waitingForInfo': // waitingForInfo
//         return 'في انتظار معلومات';
//       case 'pending': // pending
//         return 'قيد الانتظار';
//       case 'cancelled': // cancelled
//         return 'ملغاة';
//       case 'news': // news
//         return 'جديدة';
//       default:
//         return 'غير معروفة';
//     }
//   }

//   /// الحصول على نص أولوية المهمة
//   String _getTaskPriorityText(String priority) {
//     switch (priority) {
//       case 'urgent':
//         return 'عاجلة';
//       case 'high':
//         return 'مرتفعة';
//       case 'medium':
//         return 'متوسطة';
//       case 'low':
//         return 'منخفضة';
//       default:
//         return 'غير محددة';
//     }
//   }

//   /// تصدير الرسم البياني
//   void _exportChart(String chartType) {
//     showDialog(
//       context: context,
//       builder: (context) => AlertDialog(
//         title: const Text('تصدير الرسم البياني'),
//         content: Column(
//           mainAxisSize: MainAxisSize.min,
//           children: [
//             const Text('اختر تنسيق التصدير:'),
//             const SizedBox(height: 16),
//             Row(
//               mainAxisAlignment: MainAxisAlignment.spaceEvenly,
//               children: [
//                 ElevatedButton(
//                   onPressed: () {
//                     Navigator.of(context).pop();
//                     _exportToPdf(chartType);
//                   },
//                   child: const Text('PDF'),
//                 ),
//                 ElevatedButton(
//                   onPressed: () {
//                     Navigator.of(context).pop();
//                     _exportToExcel(chartType);
//                   },
//                   child: const Text('Excel'),
//                 ),
//               ],
//             ),
//           ],
//         ),
//         actions: [
//           TextButton(
//             onPressed: () => Navigator.of(context).pop(),
//             child: const Text('إلغاء'),
//           ),
//         ],
//       ),
//     );
//   }

//   /// تصدير إلى PDF
//   void _exportToPdf(String chartType) {
//     // هنا يمكن إضافة وظيفة تصدير التقرير إلى PDF
//     ScaffoldMessenger.of(context).showSnackBar(
//       const SnackBar(
//         content: Text('جاري تصدير التقرير إلى PDF...'),
//         duration: Duration(seconds: 2),
//       ),
//     );
//   }

//   /// تصدير إلى Excel
//   void _exportToExcel(String chartType) {
//     // هنا يمكن إضافة وظيفة تصدير التقرير إلى Excel
//     ScaffoldMessenger.of(context).showSnackBar(
//       const SnackBar(
//         content: Text('جاري تصدير التقرير إلى Excel...'),
//         duration: Duration(seconds: 2),
//       ),
//     );
//   }
// }
