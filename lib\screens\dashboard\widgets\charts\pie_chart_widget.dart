import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:syncfusion_flutter_charts/charts.dart';
import '../../models/dashboard_models.dart';
import '../base_chart_widget.dart';
import '../../../../controllers/task_controller.dart';
import '../../../../controllers/department_controller.dart';


/// مخطط دائري بسيط باستخدام BaseChartWidget
class PieChartWidget extends BaseChartWidget {
  const PieChartWidget({
    super.key,
    required super.item,
    super.filters,
    super.showHeader = true,
    super.showFilters = false,
    super.onRefresh,
  });

  @override
  Widget buildChartContent(BuildContext context, List<ChartData> data) {
    // الحصول على نوع البيانات من config
    final dataType = item.config['dataType'] as String?;

    // إنشاء البيانات حسب النوع
    List<ChartData> chartData;
    switch (dataType) {
      case 'priority_distribution':
        chartData = _buildPriorityData();
        break;
      case 'task_status':
        chartData = _buildTaskStatusData();
        break;
      default:
        chartData = data.isNotEmpty ? data : _buildDefaultData();
    }

    if (chartData.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.pie_chart, size: 48, color: Colors.grey),
            SizedBox(height: 16),
            Text('لا توجد بيانات لعرضها'),
          ],
        ),
      );
    }

    return SfCircularChart(
      legend: Legend(
        isVisible: true,
        position: LegendPosition.bottom,
        textStyle: const TextStyle(fontSize: 10),
      ),
      series: <CircularSeries>[
        PieSeries<ChartData, String>(
          dataSource: chartData,
          xValueMapper: (ChartData data, _) => data.label,
          yValueMapper: (ChartData data, _) => data.value,
          pointColorMapper: (ChartData data, _) => data.color,
          dataLabelSettings: const DataLabelSettings(
            isVisible: true,
            labelPosition: ChartDataLabelPosition.outside,
            textStyle: TextStyle(fontSize: 10),
          ),
          enableTooltip: true,
        ),
      ],
      tooltipBehavior: TooltipBehavior(enable: true),
    );
  }

  /// بناء بيانات توزيع الأولوية
  List<ChartData> _buildPriorityData() {
    final taskController = Get.find<TaskController>();
    final tasks = taskController.allTasks;
    final priorityCount = <String, int>{};

    for (final task in tasks) {
      if (!task.isDeleted) {
        priorityCount[task.priority] = (priorityCount[task.priority] ?? 0) + 1;
      }
    }

    return priorityCount.entries.map((entry) => ChartData(
      label: entry.key,
      value: entry.value.toDouble(),
      color: _getPriorityColor(entry.key),
    )).toList();
  }

  /// بناء بيانات حالة المهام
  List<ChartData> _buildTaskStatusData() {
    final taskController = Get.find<TaskController>();
    final taskStats = taskController.taskStats;

    return [
      ChartData(label: 'معلقة', value: taskStats['pending']?.toDouble() ?? 0, color: Colors.orange),
      ChartData(label: 'قيد التنفيذ', value: taskStats['inProgress']?.toDouble() ?? 0, color: Colors.blue),
      ChartData(label: 'مكتملة', value: taskStats['completed']?.toDouble() ?? 0, color: Colors.green),
      ChartData(label: 'متأخرة', value: taskStats['overdue']?.toDouble() ?? 0, color: Colors.red),
    ].where((data) => data.value > 0).toList();
  }

  /// بناء بيانات افتراضية
  List<ChartData> _buildDefaultData() {
    return [
      ChartData(label: 'عينة 1', value: 30, color: Colors.blue),
      ChartData(label: 'عينة 2', value: 25, color: Colors.green),
      ChartData(label: 'عينة 3', value: 20, color: Colors.orange),
    ];
  }

  /// الحصول على لون الأولوية
  Color _getPriorityColor(String priority) {
    switch (priority.toLowerCase()) {
      case 'عالية':
      case 'high':
        return Colors.red;
      case 'متوسطة':
      case 'medium':
        return Colors.orange;
      case 'منخفضة':
      case 'low':
        return Colors.green;
      default:
        return Colors.blue;
    }
  }

}
