import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:syncfusion_flutter_charts/charts.dart';
import '../../../../constants/app_colors.dart';
import '../../../../controllers/task_controller.dart';
import '../../../../controllers/user_controller.dart';
import '../base_chart_widget.dart';
import '../../models/dashboard_models.dart';

/// Time Tracking Widget - تتبع الوقت المستغرق في المهام والمشاريع
class TimeTrackingWidget extends BaseChartWidget {
  final String? title;
  final String? subtitle;
  final Map<String, dynamic>? config;

  const TimeTrackingWidget({
    super.key,
    this.title,
    this.subtitle,
    this.config,
    required super.item,
    super.filters,
    super.showHeader,
    super.showFilters,
    super.onRefresh,
  });

  @override
  Widget buildChartContent(BuildContext context, List<ChartData> data) {
    return TimeTrackingContent(
      title: title,
      subtitle: subtitle,
      config: config,
    );
  }
}

/// محتوى Time Tracking Widget المنفصل
class TimeTrackingContent extends StatefulWidget {
  final String? title;
  final String? subtitle;
  final Map<String, dynamic>? config;

  const TimeTrackingContent({
    super.key,
    this.title,
    this.subtitle,
    this.config,
  });

  @override
  State<TimeTrackingContent> createState() => _TimeTrackingContentState();
}

class _TimeTrackingContentState extends State<TimeTrackingContent>
    with TickerProviderStateMixin {
  
  late final TaskController _taskController;
  late final UserController _userController;
  
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  
  List<TimeTrackingData> _timeData = [];
  bool _isLoading = true;
  String _selectedPeriod = 'today'; // today, week, month
  double _totalHours = 0.0;
  double _averageHours = 0.0;

  @override
  void initState() {
    super.initState();
    _initializeControllers();
    _initializeAnimations();
    _loadData();
  }

  void _initializeControllers() {
    _taskController = Get.find<TaskController>();
    _userController = Get.find<UserController>();
  }

  void _initializeAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  Future<void> _loadData() async {
    setState(() => _isLoading = true);

    try {
      final timeDataList = <TimeTrackingData>[];
      final now = DateTime.now();
      
      // فلترة المهام حسب الفترة المحددة
      final filteredTasks = _taskController.allTasks.where((task) {
        if (task.isDeleted) return false;
        
        final taskDate = task.createdAtDateTime;
        
        switch (_selectedPeriod) {
          case 'today':
            return taskDate.day == now.day && 
                   taskDate.month == now.month && 
                   taskDate.year == now.year;
          case 'week':
            final weekStart = now.subtract(Duration(days: now.weekday - 1));
            return taskDate.isAfter(weekStart.subtract(const Duration(days: 1)));
          case 'month':
            return taskDate.month == now.month && taskDate.year == now.year;
          default:
            return true;
        }
      }).toList();

      // حساب الوقت لكل مستخدم
      final userTimeMap = <int, double>{};
      
      for (final task in filteredTasks) {
        if (task.assigneeId != null) {
          // حساب الوقت المستغرق (افتراضي بناءً على تعقيد المهمة)
          final estimatedHours = _calculateTaskTime(task);
          userTimeMap[task.assigneeId!] = (userTimeMap[task.assigneeId!] ?? 0) + estimatedHours;
        }
      }

      // إنشاء بيانات التتبع
      for (final entry in userTimeMap.entries) {
        final user = _userController.users.firstWhereOrNull(
          (u) => u.id == entry.key,
        );
        
        if (user != null) {
          final userTasks = filteredTasks.where((task) => task.assigneeId == entry.key).length;
          
          timeDataList.add(TimeTrackingData(
            userName: user.name,
            userId: entry.key,
            totalHours: entry.value,
            tasksCount: userTasks,
            averageHoursPerTask: userTasks > 0 ? entry.value / userTasks : 0.0,
            efficiency: _calculateEfficiency(entry.value, userTasks),
          ));
        }
      }

      // ترتيب حسب الوقت الإجمالي
      timeDataList.sort((a, b) => b.totalHours.compareTo(a.totalHours));

      // حساب الإحصائيات الإجمالية
      _totalHours = timeDataList.fold(0.0, (sum, data) => sum + data.totalHours);
      _averageHours = timeDataList.isNotEmpty ? _totalHours / timeDataList.length : 0.0;

      setState(() {
        _timeData = timeDataList;
        _isLoading = false;
      });

      _animationController.forward();
    } catch (e) {
      debugPrint('خطأ في تحميل بيانات Time Tracking Widget: $e');
      setState(() => _isLoading = false);
    }
  }

  double _calculateTaskTime(dynamic task) {
    // حساب تقديري للوقت بناءً على خصائص المهمة
    double baseHours = 2.0; // وقت أساسي
    
    // إضافة وقت حسب الأولوية
    switch (task.priority) {
      case 'high':
        baseHours += 2.0;
        break;
      case 'medium':
        baseHours += 1.0;
        break;
      case 'low':
        baseHours += 0.5;
        break;
    }
    
    // إضافة وقت حسب الحالة
    if (task.status == 'completed') {
      baseHours *= 1.0; // الوقت الفعلي
    } else if (task.status == 'in_progress') {
      baseHours *= 0.7; // جزء من الوقت
    } else {
      baseHours *= 0.1; // وقت التخطيط فقط
    }
    
    return baseHours;
  }

  double _calculateEfficiency(double totalHours, int tasksCount) {
    if (tasksCount == 0) return 0.0;
    
    final averageTime = totalHours / tasksCount;
    
    // كفاءة عالية = وقت أقل لكل مهمة
    if (averageTime <= 1.0) return 100.0;
    if (averageTime <= 2.0) return 80.0;
    if (averageTime <= 3.0) return 60.0;
    if (averageTime <= 4.0) return 40.0;
    return 20.0;
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildHeader(),
          const SizedBox(height: 16),
          _buildPeriodSelector(),
          const SizedBox(height: 16),
          _buildSummaryCards(),
          const SizedBox(height: 16),
          Expanded(
            child: _isLoading ? _buildLoadingState() : _buildTimeChart(),
          ),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: AppColors.primary.withOpacity(0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            Icons.access_time,
            color: AppColors.primary,
            size: 20,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                widget.title ?? 'تتبع الوقت',
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Colors.black87,
                ),
              ),
              if (widget.subtitle != null) ...[
                const SizedBox(height: 4),
                Text(
                  widget.subtitle!,
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey.shade600,
                  ),
                ),
              ],
            ],
          ),
        ),
        IconButton(
          icon: const Icon(Icons.refresh, size: 20),
          onPressed: _loadData,
          tooltip: 'تحديث البيانات',
        ),
      ],
    );
  }

  Widget _buildPeriodSelector() {
    return Row(
      children: [
        _buildPeriodButton('today', 'اليوم'),
        const SizedBox(width: 8),
        _buildPeriodButton('week', 'الأسبوع'),
        const SizedBox(width: 8),
        _buildPeriodButton('month', 'الشهر'),
      ],
    );
  }

  Widget _buildPeriodButton(String value, String label) {
    final isSelected = _selectedPeriod == value;
    
    return GestureDetector(
      onTap: () {
        setState(() => _selectedPeriod = value);
        _loadData();
      },
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
        decoration: BoxDecoration(
          color: isSelected ? AppColors.primary : Colors.grey.shade100,
          borderRadius: BorderRadius.circular(16),
        ),
        child: Text(
          label,
          style: TextStyle(
            fontSize: 12,
            color: isSelected ? Colors.white : Colors.black87,
            fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
          ),
        ),
      ),
    );
  }

  Widget _buildSummaryCards() {
    return Row(
      children: [
        Expanded(
          child: _buildSummaryCard(
            'إجمالي الساعات',
            '${_totalHours.toStringAsFixed(1)}س',
            Icons.schedule,
            Colors.blue,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: _buildSummaryCard(
            'متوسط الساعات',
            '${_averageHours.toStringAsFixed(1)}س',
            Icons.trending_up,
            Colors.green,
          ),
        ),
      ],
    );
  }

  Widget _buildSummaryCard(String title, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 20),
          const SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            title,
            style: const TextStyle(fontSize: 10, color: Colors.black87),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildLoadingState() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(),
          SizedBox(height: 8),
          Text(
            'جاري تحميل بيانات الوقت...',
            style: TextStyle(fontSize: 12, color: Colors.grey),
          ),
        ],
      ),
    );
  }

  Widget _buildTimeChart() {
    if (_timeData.isEmpty) {
      return const Center(
        child: Text(
          'لا توجد بيانات وقت للعرض',
          style: TextStyle(color: Colors.grey),
        ),
      );
    }

    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return FadeTransition(
          opacity: _fadeAnimation,
          child: SfCartesianChart(
            primaryXAxis: CategoryAxis(),
            primaryYAxis: NumericAxis(title: AxisTitle(text: 'الساعات')),
            series: <CartesianSeries>[
              ColumnSeries<TimeTrackingData, String>(
                name: 'الوقت المستغرق',
                dataSource: _timeData,
                xValueMapper: (TimeTrackingData data, _) => data.userName,
                yValueMapper: (TimeTrackingData data, _) => data.totalHours,
                pointColorMapper: (TimeTrackingData data, _) => _getEfficiencyColor(data.efficiency),
                dataLabelSettings: const DataLabelSettings(
                  isVisible: true,
                  textStyle: TextStyle(fontSize: 10),
                ),
              ),
            ],
            tooltipBehavior: TooltipBehavior(
              enable: true,
              format: 'point.x: point.yس',
            ),
          ),
        );
      },
    );
  }

  Color _getEfficiencyColor(double efficiency) {
    if (efficiency >= 80) return Colors.green;
    if (efficiency >= 60) return Colors.lightGreen;
    if (efficiency >= 40) return Colors.orange;
    return Colors.red;
  }
}

/// بيانات تتبع الوقت
class TimeTrackingData {
  final String userName;
  final int userId;
  final double totalHours;
  final int tasksCount;
  final double averageHoursPerTask;
  final double efficiency;

  TimeTrackingData({
    required this.userName,
    required this.userId,
    required this.totalHours,
    required this.tasksCount,
    required this.averageHoursPerTask,
    required this.efficiency,
  });
}
