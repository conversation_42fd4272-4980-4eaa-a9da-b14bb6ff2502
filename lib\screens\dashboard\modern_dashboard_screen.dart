import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../constants/app_colors.dart';
import '../../services/unified_permission_service.dart';
import 'widgets/smart_dashboard_widget.dart';
import 'widgets/quick_filters_widget.dart';
import 'widgets/board_connections_widget.dart';
import 'widgets/advanced_analytics_widget.dart';
import 'widgets/flexible_dashboard_widget.dart';

/// شاشة لوحة التحكم العصرية والذكية
class ModernDashboardScreen extends StatefulWidget {
  const ModernDashboardScreen({super.key});

  @override
  State<ModernDashboardScreen> createState() => _ModernDashboardScreenState();
}

class _ModernDashboardScreenState extends State<ModernDashboardScreen>
    with TickerProviderStateMixin {
  
  late final UnifiedPermissionService _permissionService;
  late TabController _tabController;
  
  final List<Tab> _tabs = [
    const Tab(icon: Icon(Icons.dashboard), text: 'لوحة التحكم'),
    const Tab(icon: Icon(Icons.grid_view), text: 'الداشبورد المرن'),
    const Tab(icon: Icon(Icons.link), text: 'Board Connections'),
    const Tab(icon: Icon(Icons.analytics), text: 'التحليلات'),
    const Tab(icon: Icon(Icons.trending_up), text: 'التقارير'),
    const Tab(icon: Icon(Icons.settings), text: 'الإعدادات'),
  ];

  @override
  void initState() {
    super.initState();
    _permissionService = Get.find<UnifiedPermissionService>();
    _tabController = TabController(length: _tabs.length, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // التحقق من صلاحية الوصول للوحة المعلومات
    if (!_permissionService.canAccessDashboard()) {
      return _buildNoPermissionScreen();
    }

    return Scaffold(
      backgroundColor: Colors.grey.shade50,
      appBar: _buildAppBar(),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildDashboardTab(),
          _buildFlexibleDashboardTab(),
          _buildBoardConnectionsTab(),
          _buildAnalyticsTab(),
          _buildReportsTab(),
          _buildSettingsTab(),
        ],
      ),
      floatingActionButton: _buildFloatingActionButton(),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      title: const Text(
        'لوحة التحكم الذكية',
        style: TextStyle(
          fontWeight: FontWeight.bold,
          color: Colors.white,
        ),
      ),
      backgroundColor: AppColors.primary,
      elevation: 0,
      centerTitle: true,
      actions: [
        IconButton(
          icon: const Icon(Icons.notifications, color: Colors.white),
          onPressed: _showNotifications,
          tooltip: 'الإشعارات',
        ),
        IconButton(
          icon: const Icon(Icons.refresh, color: Colors.white),
          onPressed: _refreshDashboard,
          tooltip: 'تحديث',
        ),
        PopupMenuButton<String>(
          icon: const Icon(Icons.more_vert, color: Colors.white),
          onSelected: _handleMenuSelection,
          itemBuilder: (context) => [
            const PopupMenuItem(
              value: 'export',
              child: ListTile(
                leading: Icon(Icons.download),
                title: Text('تصدير البيانات'),
              ),
            ),
            const PopupMenuItem(
              value: 'print',
              child: ListTile(
                leading: Icon(Icons.print),
                title: Text('طباعة التقرير'),
              ),
            ),
            const PopupMenuItem(
              value: 'share',
              child: ListTile(
                leading: Icon(Icons.share),
                title: Text('مشاركة'),
              ),
            ),
          ],
        ),
      ],
      bottom: TabBar(
        controller: _tabController,
        tabs: _tabs,
        indicatorColor: Colors.white,
        labelColor: Colors.white,
        unselectedLabelColor: Colors.white70,
        indicatorWeight: 3,
      ),
    );
  }

  Widget _buildNoPermissionScreen() {
    return Scaffold(
      appBar: AppBar(
        title: const Text('لوحة التحكم'),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.lock_outline,
              size: 80,
              color: Colors.grey.shade400,
            ),
            const SizedBox(height: 24),
            Text(
              'ليس لديك صلاحية للوصول إلى لوحة التحكم',
              style: TextStyle(
                fontSize: 18,
                color: Colors.grey.shade600,
                fontWeight: FontWeight.w500,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            Text(
              'يرجى التواصل مع المدير للحصول على الصلاحيات المطلوبة',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey.shade500,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 32),
            ElevatedButton.icon(
              onPressed: () => Get.back(),
              icon: const Icon(Icons.arrow_back),
              label: const Text('العودة'),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primary,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// بناء تبويب لوحة التحكم مع زر الفلاتر
  Widget _buildDashboardTab() {
    return Column(
      children: [
        // شريط الأدوات مع زر الفلاتر
        _buildDashboardToolbar(),
        // لوحة التحكم الذكية
        const Flexible(
          child: SmartDashboardWidget(),
        ),
      ],
    );
  }

  /// بناء تبويب الداشبورد المرن
  Widget _buildFlexibleDashboardTab() {
    return FlexibleDashboardWidget(
      enableDragDrop: true,
      enableResize: true,
      showGridLines: false,
      onLayoutChanged: (items) {
        // يمكن إضافة منطق حفظ التخطيط هنا
        debugPrint('تم تغيير تخطيط الداشبورد: ${items.length} عنصر');
      },
    );
  }

  /// بناء شريط الأدوات مع زر الفلاتر
  Widget _buildDashboardToolbar() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withAlpha(25),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          // عنوان الداشبورد
          const Expanded(
            child: Text(
              'لوحة التحكم الذكية',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.black87,
              ),
            ),
          ),
          // زر الفلاتر مع مؤشر الحالة
          Stack(
            children: [
              IconButton(
                onPressed: _showFiltersDialog,
                icon: const Icon(Icons.filter_list),
                tooltip: 'الفلاتر والبحث',
                style: IconButton.styleFrom(
                  backgroundColor: AppColors.primary.withAlpha(25),
                  foregroundColor: AppColors.primary,
                ),
              ),
              // مؤشر الفلاتر النشطة (يمكن إضافة منطق لإظهاره عند وجود فلاتر)
              Positioned(
                right: 8,
                top: 8,
                child: Container(
                  width: 8,
                  height: 8,
                  decoration: BoxDecoration(
                    color: Colors.orange,
                    borderRadius: BorderRadius.circular(4),
                  ),
                  // يمكن إخفاؤه عند عدم وجود فلاتر نشطة
                  // child: _hasActiveFilters ? Container() : const SizedBox.shrink(),
                ),
              ),
            ],
          ),
          const SizedBox(width: 8),
          // زر التحديث
          IconButton(
            onPressed: () {
              setState(() {});
            },
            icon: const Icon(Icons.refresh),
            tooltip: 'تحديث البيانات',
            style: IconButton.styleFrom(
              backgroundColor: Colors.grey.withAlpha(25),
              foregroundColor: Colors.grey.shade700,
            ),
          ),
        ],
      ),
    );
  }

  /// بناء تبويب Board Connections
  Widget _buildBoardConnectionsTab() {
    return const BoardConnectionsWidget();
  }

  Widget _buildAnalyticsTab() {
    return const AdvancedAnalyticsWidget();
  }

  /// إظهار حوار الفلاتر
  void _showFiltersDialog() {
    final screenWidth = MediaQuery.of(context).size.width;

    // للشاشات الصغيرة، استخدم bottom sheet
    if (screenWidth < 600) {
      _showFiltersBottomSheet();
    } else {
      // للشاشات الكبيرة، استخدم dialog
      _showFiltersDialogLarge();
    }
  }

  /// إظهار الفلاتر في bottom sheet للشاشات الصغيرة
  void _showFiltersBottomSheet() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (BuildContext context) {
        return Container(
          height: MediaQuery.of(context).size.height * 0.75,
          decoration: const BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(20),
              topRight: Radius.circular(20),
            ),
          ),
          child: Column(
            children: [
              // مقبض السحب
              Container(
                margin: const EdgeInsets.only(top: 8),
                width: 40,
                height: 4,
                decoration: BoxDecoration(
                  color: Colors.grey.shade300,
                  borderRadius: BorderRadius.circular(2),
                ),
              ),
              // رأس الحوار
              Container(
                padding: const EdgeInsets.all(20),
                child: Row(
                  children: [
                    Icon(
                      Icons.filter_list,
                      color: AppColors.primary,
                      size: 24,
                    ),
                    const SizedBox(width: 12),
                    const Expanded(
                      child: Text(
                        'الفلاتر والبحث',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: Colors.black87,
                        ),
                      ),
                    ),
                    IconButton(
                      onPressed: () => Navigator.of(context).pop(),
                      icon: const Icon(Icons.close),
                    ),
                  ],
                ),
              ),
              // محتوى الفلاتر
              Expanded(
                child: QuickFiltersWidget(
                  onFiltersChanged: () {
                    setState(() {});
                    Navigator.of(context).pop();
                  },
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  /// إظهار الفلاتر في dialog للشاشات الكبيرة
  void _showFiltersDialogLarge() {
    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (BuildContext context) {
        return Dialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          child: Container(
            width: MediaQuery.of(context).size.width * 0.8,
            constraints: const BoxConstraints(
              maxWidth: 600,
              maxHeight: 500,
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // رأس الحوار
                Container(
                  padding: const EdgeInsets.all(20),
                  decoration: BoxDecoration(
                    color: AppColors.primary,
                    borderRadius: const BorderRadius.only(
                      topLeft: Radius.circular(16),
                      topRight: Radius.circular(16),
                    ),
                  ),
                  child: Row(
                    children: [
                      const Icon(
                        Icons.filter_list,
                        color: Colors.white,
                        size: 24,
                      ),
                      const SizedBox(width: 12),
                      const Expanded(
                        child: Text(
                          'الفلاتر والبحث',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                          ),
                        ),
                      ),
                      IconButton(
                        onPressed: () => Navigator.of(context).pop(),
                        icon: const Icon(
                          Icons.close,
                          color: Colors.white,
                        ),
                      ),
                    ],
                  ),
                ),
                // محتوى الفلاتر
                Flexible(
                  child: QuickFiltersWidget(
                    onFiltersChanged: () {
                      setState(() {});
                      Navigator.of(context).pop();
                    },
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildReportsTab() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.trending_up, size: 64, color: Colors.grey),
          SizedBox(height: 16),
          Text(
            'التقارير التفصيلية',
            style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          ),
          SizedBox(height: 8),
          Text(
            'قريباً...',
            style: TextStyle(fontSize: 14, color: Colors.grey),
          ),
        ],
      ),
    );
  }

  Widget _buildSettingsTab() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.settings, size: 64, color: Colors.grey),
          SizedBox(height: 16),
          Text(
            'إعدادات لوحة التحكم',
            style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          ),
          SizedBox(height: 8),
          Text(
            'قريباً...',
            style: TextStyle(fontSize: 14, color: Colors.grey),
          ),
        ],
      ),
    );
  }

  Widget? _buildFloatingActionButton() {
    if (!_permissionService.canManageDashboards()) {
      return null;
    }

    return FloatingActionButton.extended(
      onPressed: _showCustomizationDialog,
      backgroundColor: AppColors.primary,
      foregroundColor: Colors.white,
      icon: const Icon(Icons.tune),
      label: const Text('تخصيص'),
    );
  }

  void _showNotifications() {
    Get.snackbar(
      'الإشعارات',
      'لا توجد إشعارات جديدة',
      snackPosition: SnackPosition.TOP,
      backgroundColor: AppColors.primary,
      colorText: Colors.white,
    );
  }

  void _refreshDashboard() {
    Get.snackbar(
      'تحديث',
      'تم تحديث لوحة التحكم بنجاح',
      snackPosition: SnackPosition.TOP,
      backgroundColor: Colors.green,
      colorText: Colors.white,
    );
  }

  void _handleMenuSelection(String value) {
    switch (value) {
      case 'export':
        _exportData();
        break;
      case 'print':
        _printReport();
        break;
      case 'share':
        _shareData();
        break;
    }
  }

  void _exportData() {
    Get.snackbar(
      'تصدير',
      'جاري تصدير البيانات...',
      snackPosition: SnackPosition.TOP,
      backgroundColor: Colors.blue,
      colorText: Colors.white,
    );
  }

  void _printReport() {
    Get.snackbar(
      'طباعة',
      'جاري إعداد التقرير للطباعة...',
      snackPosition: SnackPosition.TOP,
      backgroundColor: Colors.orange,
      colorText: Colors.white,
    );
  }

  void _shareData() {
    Get.snackbar(
      'مشاركة',
      'جاري إعداد البيانات للمشاركة...',
      snackPosition: SnackPosition.TOP,
      backgroundColor: Colors.purple,
      colorText: Colors.white,
    );
  }

  void _showCustomizationDialog() {
    Get.dialog(
      AlertDialog(
        title: const Text('تخصيص لوحة التحكم'),
        content: const Text('هذه الميزة قيد التطوير وستكون متاحة قريباً.'),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('موافق'),
          ),
        ],
      ),
    );
  }
}
