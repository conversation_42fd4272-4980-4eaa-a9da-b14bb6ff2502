import 'package:get/get.dart';
import 'package:flutter/material.dart';
import '../../../controllers/task_controller.dart';
import '../../../controllers/user_controller.dart';
import '../../../controllers/department_controller.dart';
import '../../../controllers/auth_controller.dart';

/// مستودع بيانات لوحة المعلومات - يستخدم البيانات الحقيقية من قاعدة البيانات
class DashboardRepository extends GetxService {
  static DashboardRepository get instance => Get.find<DashboardRepository>();

  late final TaskController _taskController;
  late final UserController _userController;
  late final DepartmentController _departmentController;
  late final AuthController _authController;

  @override
  void onInit() {
    super.onInit();
    _initializeControllers();
  }

  void _initializeControllers() {
    _taskController = Get.find<TaskController>();
    _userController = Get.find<UserController>();
    _departmentController = Get.find<DepartmentController>();
    _authController = Get.find<AuthController>();
  }

  /// الحصول على تخطيط لوحة المعلومات الافتراضي
  Future<List<Map<String, dynamic>>> getDashboardLayout(String userId) async {
    try {
      // تخطيط افتراضي بناءً على صلاحيات المستخدم
      final layouts = <Map<String, dynamic>>[];

      // مخطط توزيع المهام حسب الحالة
      layouts.add({
        'id': 'task_status_chart',
        'title': 'توزيع المهام حسب الحالة',
        'type': 'pie',
        'position': {'x': 0, 'y': 0},
        'size': {'width': 2, 'height': 2},
        'dataSource': 'task_status',
      });

      // مخطط المهام الشهرية
      layouts.add({
        'id': 'monthly_tasks_chart',
        'title': 'المهام الشهرية',
        'type': 'bar',
        'position': {'x': 2, 'y': 0},
        'size': {'width': 2, 'height': 2},
        'dataSource': 'monthly_tasks',
      });

      // مخطط معدل الإكمال
      layouts.add({
        'id': 'completion_rate_chart',
        'title': 'معدل إكمال المهام',
        'type': 'gauge',
        'position': {'x': 0, 'y': 2},
        'size': {'width': 2, 'height': 2},
        'dataSource': 'completion_rate',
      });

      // مخطط أداء المستخدمين
      layouts.add({
        'id': 'user_performance_chart',
        'title': 'أداء المستخدمين',
        'type': 'bar',
        'position': {'x': 2, 'y': 2},
        'size': {'width': 2, 'height': 2},
        'dataSource': 'user_performance',
      });

      return layouts;
    } catch (e) {
      debugPrint('خطأ في تحميل تخطيط لوحة المعلومات: $e');
      throw Exception('فشل في تحميل تخطيط لوحة المعلومات: $e');
    }
  }

  /// حفظ تخطيط لوحة المعلومات
  Future<void> saveDashboardLayout(String userId, List<Map<String, dynamic>> layout) async {
    try {
      // TODO: تنفيذ حفظ التخطيط في قاعدة البيانات
      // يمكن إضافة API endpoint لحفظ تخطيط المستخدم
      debugPrint('حفظ تخطيط لوحة المعلومات للمستخدم: $userId');
    } catch (e) {
      debugPrint('خطأ في حفظ تخطيط لوحة المعلومات: $e');
      throw Exception('فشل في حفظ تخطيط لوحة المعلومات: $e');
    }
  }

  /// الحصول على بيانات المخطط الحقيقية من Controllers
  Future<Map<String, dynamic>?> getChartData(String chartId) async {
    try {
      switch (chartId) {
        // KPI Charts
        case 'kpi_total_tasks':
        case 'kpi_completed_tasks':
        case 'kpi_pending_tasks':
        case 'kpi_users':
          return await _getKPIData(chartId);

        // Main Charts
        case 'task_status':
        case 'task_status_pie':
          return await _getTaskStatusData();
        case 'monthly_tasks':
        case 'monthly_tasks_bar':
          return await _getMonthlyTasksData();
        case 'completion_rate':
        case 'task_completion_gauge':
          return await _getCompletionRateData();
        case 'user_performance':
          return await _getUserPerformanceData();

        // Advanced Widgets
        case 'calendar_tasks':
          return await _getCalendarTasksData();
        case 'numbers_stats':
          return await _getNumbersStatsData();
        case 'battery_progress':
          return await _getBatteryProgressData();
        case 'time_tracking':
          return await _getTimeTrackingData();
        case 'workload_distribution':
          return await _getWorkloadDistributionData();

        default:
          debugPrint('لم يتم العثور على بيانات للمخطط: $chartId');
          return _getDefaultChartData(chartId);
      }
    } catch (e) {
      debugPrint('خطأ في تحميل بيانات المخطط $chartId: $e');
      return null;
    }
  }

  /// الحصول على بيانات توزيع المهام حسب الحالة
  Future<Map<String, dynamic>> _getTaskStatusData() async {
    final taskStats = _taskController.taskStats;

    return {
      'labels': ['معلقة', 'قيد التنفيذ', 'مكتملة', 'متأخرة'],
      'values': [
        taskStats['pending'] ?? 0,
        taskStats['inProgress'] ?? 0,
        taskStats['completed'] ?? 0,
        taskStats['overdue'] ?? 0,
      ],
      'colors': [
        '#FFA726', // برتقالي للمعلقة
        '#42A5F5', // أزرق لقيد التنفيذ
        '#66BB6A', // أخضر للمكتملة
        '#EF5350', // أحمر للمتأخرة
      ],
    };
  }

  /// الحصول على بيانات المهام الشهرية
  Future<Map<String, dynamic>> _getMonthlyTasksData() async {
    final tasks = _taskController.allTasks;
    final monthlyData = <String, int>{};

    // تجميع المهام حسب الشهر
    for (final task in tasks) {
      if (!task.isDeleted) {
        final month = '${task.createdAtDateTime.year}-${task.createdAtDateTime.month.toString().padLeft(2, '0')}';
        monthlyData[month] = (monthlyData[month] ?? 0) + 1;
      }
    }

    // أخذ آخر 6 أشهر
    final sortedEntries = monthlyData.entries.toList()
      ..sort((a, b) => a.key.compareTo(b.key));
    final recentEntries = sortedEntries.length > 6
        ? sortedEntries.sublist(sortedEntries.length - 6)
        : sortedEntries;

    return {
      'labels': recentEntries.map((e) => e.key).toList(),
      'values': recentEntries.map((e) => e.value).toList(),
      'colors': ['#42A5F5'], // أزرق موحد
    };
  }

  /// الحصول على بيانات معدل الإكمال
  Future<Map<String, dynamic>> _getCompletionRateData() async {
    final taskStats = _taskController.taskStats;
    final total = taskStats['total'] ?? 0;
    final completed = taskStats['completed'] ?? 0;

    final completionRate = total > 0 ? (completed / total * 100).round() : 0;

    return {
      'value': completionRate,
      'label': 'معدل الإكمال',
      'total': total,
      'completed': completed,
    };
  }

  /// الحصول على بيانات أداء المستخدمين
  Future<Map<String, dynamic>> _getUserPerformanceData() async {
    final tasks = _taskController.allTasks;
    final userPerformance = <String, int>{};

    // تجميع المهام حسب المستخدم المسند إليه
    for (final task in tasks) {
      if (!task.isDeleted && task.assigneeId != null) {
        final user = _userController.users.firstWhereOrNull(
          (u) => u.id == task.assigneeId,
        );
        if (user != null) {
          userPerformance[user.name] = (userPerformance[user.name] ?? 0) + 1;
        }
      }
    }

    // أخذ أفضل 5 مستخدمين
    final sortedEntries = userPerformance.entries.toList()
      ..sort((a, b) => b.value.compareTo(a.value));
    final topUsers = sortedEntries.length > 5
        ? sortedEntries.sublist(0, 5)
        : sortedEntries;

    return {
      'labels': topUsers.map((e) => e.key).toList(),
      'values': topUsers.map((e) => e.value).toList(),
      'colors': ['#FF7043'], // برتقالي موحد
    };
  }

  /// الحصول على بيانات KPI
  Future<Map<String, dynamic>> _getKPIData(String chartId) async {
    final tasks = _taskController.allTasks;
    final users = _userController.users;

    switch (chartId) {
      case 'kpi_total_tasks':
        return {
          'value': tasks.where((t) => !t.isDeleted).length,
          'label': 'إجمالي المهام',
          'icon': 'task_alt',
          'color': '#2196F3',
        };
      case 'kpi_completed_tasks':
        return {
          'value': tasks.where((t) => !t.isDeleted && t.status == 'completed').length,
          'label': 'المهام المكتملة',
          'icon': 'check_circle',
          'color': '#4CAF50',
        };
      case 'kpi_pending_tasks':
        return {
          'value': tasks.where((t) => !t.isDeleted && t.status == 'pending').length,
          'label': 'المهام المعلقة',
          'icon': 'pending',
          'color': '#FF9800',
        };
      case 'kpi_users':
        return {
          'value': users.length,
          'label': 'المستخدمين النشطين',
          'icon': 'people',
          'color': '#9C27B0',
        };
      default:
        return {'value': 0, 'label': 'غير محدد', 'icon': 'help', 'color': '#757575'};
    }
  }

  /// الحصول على بيانات التقويم
  Future<Map<String, dynamic>> _getCalendarTasksData() async {
    final tasks = _taskController.allTasks.where((t) => !t.isDeleted).toList();
    final tasksByDate = <String, int>{};

    for (final task in tasks) {
      if (task.dueDate != null) {
        // تحويل Unix timestamp إلى DateTime
        final dueDateTime = DateTime.fromMillisecondsSinceEpoch(task.dueDate! * 1000);
        final dateKey = '${dueDateTime.year}-${dueDateTime.month.toString().padLeft(2, '0')}-${dueDateTime.day.toString().padLeft(2, '0')}';
        tasksByDate[dateKey] = (tasksByDate[dateKey] ?? 0) + 1;
      }
    }

    return {
      'tasksByDate': tasksByDate,
      'totalTasks': tasks.length,
    };
  }

  /// الحصول على بيانات الأرقام والإحصائيات
  Future<Map<String, dynamic>> _getNumbersStatsData() async {
    final tasks = _taskController.allTasks.where((t) => !t.isDeleted).toList();
    final users = _userController.users;
    final departments = _departmentController.departments;

    return {
      'stats': [
        {'label': 'إجمالي المهام', 'value': tasks.length, 'icon': 'task_alt'},
        {'label': 'المستخدمين', 'value': users.length, 'icon': 'people'},
        {'label': 'الأقسام', 'value': departments.length, 'icon': 'business'},
        {'label': 'متوسط المهام/مستخدم', 'value': users.isNotEmpty ? (tasks.length / users.length).round() : 0, 'icon': 'trending_up'},
      ]
    };
  }

  /// الحصول على بيانات مؤشر البطارية
  Future<Map<String, dynamic>> _getBatteryProgressData() async {
    final tasks = _taskController.allTasks.where((t) => !t.isDeleted).toList();
    final completedTasks = tasks.where((t) => t.statusId == 3).length;
    final totalTasks = tasks.length;
    final percentage = totalTasks > 0 ? (completedTasks / totalTasks * 100).round() : 0;

    return {
      'percentage': percentage,
      'completed': completedTasks,
      'total': totalTasks,
      'label': 'معدل الإكمال',
    };
  }

  /// الحصول على بيانات تتبع الوقت
  Future<Map<String, dynamic>> _getTimeTrackingData() async {
    // بيانات وهمية لتتبع الوقت - يمكن ربطها بنظام تتبع الوقت الفعلي
    return {
      'todayHours': 8.5,
      'weekHours': 42.0,
      'monthHours': 168.0,
      'averageDaily': 8.2,
    };
  }

  /// الحصول على بيانات توزيع أعباء العمل
  Future<Map<String, dynamic>> _getWorkloadDistributionData() async {
    final tasks = _taskController.allTasks.where((t) => !t.isDeleted).toList();
    final users = _userController.users;
    final workload = <String, int>{};

    for (final user in users) {
      final userTasks = tasks.where((t) => t.assigneeId == user.id).length;
      workload[user.name] = userTasks;
    }

    return {
      'workload': workload,
      'totalTasks': tasks.length,
      'averageLoad': users.isNotEmpty ? (tasks.length / users.length).round() : 0,
    };
  }

  /// الحصول على بيانات افتراضية للمخططات غير المعرفة
  Map<String, dynamic> _getDefaultChartData(String chartId) {
    return {
      'message': 'لا توجد بيانات متاحة لهذا المخطط',
      'chartId': chartId,
      'isEmpty': true,
    };
  }
}
