import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:syncfusion_flutter_charts/charts.dart';

// import '../../constants/app_styles.dart';
import '../../models/power_bi_models.dart';

import '../../controllers/power_bi_controller.dart';


import 'create_power_bi_report_screen.dart';
import '../../services/unified_permission_service.dart';

/// فئة بيانات النقاط للرسم البياني النقطي
class ScatterData {
  final double x;
  final double y;

  ScatterData(this.x, this.y);
}

/// شاشة تفاصيل تقرير باور بي آي
///
/// توفر واجهة لعرض تفاصيل تقرير باور بي آي والرسم البياني الخاص به
class PowerBIReportDetailsScreen extends StatefulWidget {
  final String reportId;

  const PowerBIReportDetailsScreen({super.key, required this.reportId});

  @override
  State<PowerBIReportDetailsScreen> createState() =>
      _PowerBIReportDetailsScreenState();
}

class _PowerBIReportDetailsScreenState
    extends State<PowerBIReportDetailsScreen> {
  final PowerBIController _powerBIController = Get.find<PowerBIController>();

  // قائمة الأعمدة المخزنة مؤقتًا لكل جدول
  final Map<String, List<String>> _cachedColumns = {};

  @override
  void initState() {
    super.initState();

    // تأخير تحميل البيانات حتى اكتمال بناء الواجهة
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      try {
        await _loadReport();

        // تحميل أعمدة الجدول بعد تحميل التقرير
        final report = _powerBIController.currentReport.value;
        if (report != null) {
          // التحقق من صحة بيانات التقرير
          if (report.tableName.isEmpty) {
            debugPrint('خطأ: اسم الجدول فارغ في التقرير');
            _powerBIController.errorMessage.value =
                'خطأ في بيانات التقرير: اسم الجدول فارغ';
            return;
          }

          await _loadTableColumns(report.tableName);

          // التحقق من صحة أعمدة المحاور
          final columns = _cachedColumns[report.tableName] ?? [];
          if (columns.isNotEmpty) {
            if (!columns.contains(report.xAxisColumn)) {
              debugPrint(
                  'خطأ: عمود المحور الأفقي غير موجود في الجدول: ${report.xAxisColumn}');
              _powerBIController.errorMessage.value =
                  'خطأ في بيانات التقرير: عمود المحور الأفقي غير موجود';
            }

            if (!columns.contains(report.yAxisColumn)) {
              debugPrint(
                  'خطأ: عمود المحور الرأسي غير موجود في الجدول: ${report.yAxisColumn}');
              _powerBIController.errorMessage.value =
                  'خطأ في بيانات التقرير: عمود المحور الرأسي غير موجود';
            }
          }

          if (mounted) {
            setState(() {}); // تحديث الواجهة بعد تحميل الأعمدة
          }
        }
      } catch (e) {
        debugPrint('خطأ في تهيئة الشاشة: $e');
        _powerBIController.errorMessage.value = 'خطأ في تحميل التقرير: $e';
      }
    });
  }

  /// تحميل التقرير
  Future<void> _loadReport() async {
    try {
      debugPrint('بدء تحميل التقرير: ${widget.reportId}');
      await _powerBIController.loadReport(widget.reportId);
      debugPrint('تم تحميل التقرير بنجاح');

      // طباعة معلومات عن التقرير المحمل
      final report = _powerBIController.currentReport.value;
      if (report != null) {
        debugPrint('عنوان التقرير: ${report.title}');
        debugPrint('نوع الرسم البياني: ${report.chartType}');
        debugPrint('الجدول: ${report.tableName}');
        debugPrint('الأعمدة: ${report.columnNames.join(', ')}');
        debugPrint('عمود المحور الأفقي: ${report.xAxisColumn}');
        debugPrint('عمود المحور الرأسي: ${report.yAxisColumn}');
      } else {
        debugPrint('لم يتم العثور على التقرير');
      }

      // طباعة معلومات عن بيانات الرسم البياني
      final chartData = _powerBIController.chartData;
      debugPrint('مفاتيح بيانات الرسم البياني: ${chartData.keys.toList()}');
    } catch (e) {
      debugPrint('خطأ في تحميل التقرير: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تحميل التقرير: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Obx(() => Text(
            _powerBIController.currentReport.value?.title ?? 'تفاصيل التقرير')),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            tooltip: 'تحديث',
            onPressed: () {
              // تأخير تحميل البيانات حتى اكتمال بناء الواجهة
              WidgetsBinding.instance.addPostFrameCallback((_) {
                _loadReport();
              });
            },
          ),
          Obx(() {
            final report = _powerBIController.currentReport.value;
            if (report != null) {
              return IconButton(
                icon: const Icon(Icons.edit),
                tooltip: 'تعديل',
                onPressed: () {
                  Navigator.of(context)
                      .push(
                    MaterialPageRoute(
                      builder: (context) =>
                          CreatePowerBIReportScreen(report: report),
                    ),
                  )
                      .then((_) {
                    // تأخير تحميل البيانات حتى اكتمال بناء الواجهة
                    WidgetsBinding.instance.addPostFrameCallback((_) {
                      _loadReport();
                    });
                  });
                },
              );
            }
            return const SizedBox.shrink();
          }),
        ],
      ),
      body: Obx(() {
        if (_powerBIController.isLoading.value) {
          return const Center(child: CircularProgressIndicator());
        }

        if (_powerBIController.errorMessage.value.isNotEmpty) {
          return Center(
            child: Text(
              _powerBIController.errorMessage.value,
              style: const TextStyle(color: Colors.red),
            ),
          );
        }

        final report = _powerBIController.currentReport.value;
        if (report == null) {
          return const Center(
            child: Text('التقرير غير موجود'),
          );
        }

        return _buildReportDetails(report);
      }),
    );
  }

  /// بناء تفاصيل التقرير
  Widget _buildReportDetails(PowerBIReport report) {
    // ألوان التصميم الجديد
    const Color primaryColor = Color(0xFF2E5BFF);
    const Color secondaryColor = Color(0xFF00C1FF);
    const Color accentColor = Color(0xFFFF6B6B);
    const Color backgroundColor = Color(0xFFF5F7FA);
    const Color textColor = Color(0xFF2E384D);
  //الصلاحيات
    final UnifiedPermissionService _permissionService = Get.find<UnifiedPermissionService>();

    return Container(
      color: backgroundColor,
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // معلومات التقرير
            _buildModernCard(
              title: 'معلومات التقرير',
              icon: Icons.info_outline,
              iconColor: primaryColor,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildModernInfoRow(
                    icon: Icons.title,
                    label: 'العنوان',
                    value: report.title,
                    iconColor: primaryColor,
                  ),
                  if (report.description != null &&
                      report.description!.isNotEmpty)
                    _buildModernInfoRow(
                      icon: Icons.description,
                      label: 'الوصف',
                      value: report.description!,
                      iconColor: primaryColor,
                    ),
                  _buildModernInfoRow(
                    icon: Icons.table_chart,
                    label: 'الجدول',
                    value: report.tableName,
                    iconColor: primaryColor,
                  ),
                  _buildModernInfoRow(
                    icon: Icons.calendar_today,
                    label: 'تاريخ الإنشاء',
                    value: _formatDate(report.createdAt),
                    iconColor: primaryColor,
                  ),
                  if (report.updatedAt != null)
                    _buildModernInfoRow(
                      icon: Icons.update,
                      label: 'آخر تحديث',
                      value: _formatDate(report.updatedAt!),
                      iconColor: primaryColor,
                    ),
                ],
              ),
            ),
            const SizedBox(height: 20),

            // إعدادات الرسم البياني القابلة للتعديل
            _buildModernCard(
              title: 'إعدادات الرسم البياني',
              icon: Icons.settings,
              iconColor: secondaryColor,
              actions: [
                if (_permissionService.canEditPowerBIReports())
                  ElevatedButton.icon(
                    icon: const Icon(Icons.save),
                    label: const Text('حفظ التغييرات'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: accentColor,
                      foregroundColor: Colors.white,
                      elevation: 0,
                      padding: const EdgeInsets.symmetric(
                          horizontal: 16, vertical: 10),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                  onPressed: () async {
                    final success =
                        await _powerBIController.saveReportChanges();
                    if (mounted) {
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: Text(
                            success
                                ? 'تم حفظ التغييرات بنجاح'
                                : 'فشل في حفظ التغييرات',
                          ),
                          backgroundColor: success ? Colors.green : Colors.red,
                          behavior: SnackBarBehavior.floating,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(10),
                          ),
                          margin: const EdgeInsets.all(10),
                        ),
                      );
                    }
                  },
                ),
              ],
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // اختيار نوع الرسم البياني
                  Container(
                    margin: const EdgeInsets.only(bottom: 20),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Icon(Icons.category,
                                color: secondaryColor, size: 20),
                            const SizedBox(width: 8),
                            const Text(
                              'نوع الرسم البياني:',
                              style: TextStyle(
                                fontWeight: FontWeight.bold,
                                fontSize: 16,
                                color: textColor,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 12),
                        Obx(() {
                          final currentType = _powerBIController
                                  .currentReport.value?.chartType ??
                              PowerBIChartType.bar;
                          return Container(
                            decoration: BoxDecoration(
                              color: Colors.grey.shade50,
                              borderRadius: BorderRadius.circular(12),
                            ),
                            padding: const EdgeInsets.all(12),
                            child: Wrap(
                              spacing: 10,
                              runSpacing: 10,
                              children: [
                                _buildModernChartTypeButton(
                                  type: PowerBIChartType.bar,
                                  icon: Icons.bar_chart,
                                  label: 'شريطي',
                                  isSelected:
                                      currentType == PowerBIChartType.bar,
                                  primaryColor: primaryColor,
                                ),
                                _buildModernChartTypeButton(
                                  type: PowerBIChartType.line,
                                  icon: Icons.show_chart,
                                  label: 'خطي',
                                  isSelected:
                                      currentType == PowerBIChartType.line,
                                  primaryColor: primaryColor,
                                ),
                                _buildModernChartTypeButton(
                                  type: PowerBIChartType.pie,
                                  icon: Icons.pie_chart,
                                  label: 'دائري',
                                  isSelected:
                                      currentType == PowerBIChartType.pie,
                                  primaryColor: primaryColor,
                                ),
                                _buildModernChartTypeButton(
                                  type: PowerBIChartType.scatter,
                                  icon: Icons.scatter_plot,
                                  label: 'نقطي',
                                  isSelected:
                                      currentType == PowerBIChartType.scatter,
                                  primaryColor: primaryColor,
                                ),
                                _buildModernChartTypeButton(
                                  type: PowerBIChartType.bubble,
                                  icon: Icons.bubble_chart,
                                  label: 'فقاعي',
                                  isSelected:
                                      currentType == PowerBIChartType.bubble,
                                  primaryColor: primaryColor,
                                ),
                                _buildModernChartTypeButton(
                                  type: PowerBIChartType.table,
                                  icon: Icons.table_chart,
                                  label: 'جدول',
                                  isSelected:
                                      currentType == PowerBIChartType.table,
                                  primaryColor: primaryColor,
                                ),
                              ],
                            ),
                          );
                        }),
                      ],
                    ),
                  ),

                  // اختيار الأعمدة
                  Container(
                    margin: const EdgeInsets.only(bottom: 20),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Icon(Icons.view_column,
                                color: secondaryColor, size: 20),
                            const SizedBox(width: 8),
                            const Text(
                              'أعمدة البيانات:',
                              style: TextStyle(
                                fontWeight: FontWeight.bold,
                                fontSize: 16,
                                color: textColor,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 12),
                        Container(
                          decoration: BoxDecoration(
                            color: Colors.grey.shade50,
                            borderRadius: BorderRadius.circular(12),
                          ),
                          padding: const EdgeInsets.all(16),
                          child: Column(
                            children: [
                              // المحور الأفقي
                              _buildModernColumnSelector(
                                icon: Icons.swap_horiz,
                                label: 'المحور الأفقي (X):',
                                tableName: report.tableName,
                                currentValue: report.xAxisColumn,
                                onChanged: (newColumn) {
                                  if (newColumn != null &&
                                      newColumn != report.xAxisColumn) {
                                    _powerBIController.updateChartColumns(
                                      newXAxisColumn: newColumn,
                                    );
                                  }
                                },
                                iconColor: secondaryColor,
                              ),
                              const SizedBox(height: 16),

                              // المحور الرأسي
                              _buildModernColumnSelector(
                                icon: Icons.swap_vert,
                                label: 'المحور الرأسي (Y):',
                                tableName: report.tableName,
                                currentValue: report.yAxisColumn,
                                onChanged: (newColumn) {
                                  if (newColumn != null &&
                                      newColumn != report.yAxisColumn) {
                                    _powerBIController.updateChartColumns(
                                      newYAxisColumn: newColumn,
                                    );
                                  }
                                },
                                iconColor: secondaryColor,
                              ),

                              // عمود اللون (للرسوم البيانية الخطية)
                              Obx(() {
                                final currentType = _powerBIController
                                    .currentReport.value?.chartType;
                                if (currentType == PowerBIChartType.line) {
                                  return Column(
                                    children: [
                                      const SizedBox(height: 16),
                                      _buildModernColumnSelector(
                                        icon: Icons.color_lens,
                                        label: 'عمود اللون:',
                                        tableName: report.tableName,
                                        currentValue: report.colorColumn ?? '',
                                        onChanged: (newColumn) {
                                          _powerBIController.updateChartColumns(
                                            newColorColumn: newColumn,
                                          );
                                        },
                                        allowEmpty: true,
                                        iconColor: secondaryColor,
                                      ),
                                    ],
                                  );
                                }
                                return const SizedBox.shrink();
                              }),

                              // عمود الحجم (للرسوم البيانية الفقاعية)
                              Obx(() {
                                final currentType = _powerBIController
                                    .currentReport.value?.chartType;
                                if (currentType == PowerBIChartType.bubble) {
                                  return Column(
                                    children: [
                                      const SizedBox(height: 16),
                                      _buildModernColumnSelector(
                                        icon: Icons.bubble_chart,
                                        label: 'عمود الحجم:',
                                        tableName: report.tableName,
                                        currentValue: report.sizeColumn ?? '',
                                        onChanged: (newColumn) {
                                          _powerBIController.updateChartColumns(
                                            newSizeColumn: newColumn,
                                          );
                                        },
                                        allowEmpty: true,
                                        iconColor: secondaryColor,
                                      ),
                                    ],
                                  );
                                }
                                return const SizedBox.shrink();
                              }),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 20),

            // الرسم البياني
            _buildModernCard(
              title: 'الرسم البياني',
              icon: Icons.insert_chart,
              iconColor: accentColor,
              child: Container(
                height: 400,
                decoration: BoxDecoration(
                  color: Colors.grey.shade50,
                  borderRadius: BorderRadius.circular(12),
                ),
                padding: const EdgeInsets.all(16),
                child: _buildChart(report),
              ),
            ),
            const SizedBox(height: 20),

            // البيانات
            _buildModernCard(
              title: 'البيانات',
              icon: Icons.table_rows,
              iconColor: primaryColor,
              child: Container(
                decoration: BoxDecoration(
                  color: Colors.grey.shade50,
                  borderRadius: BorderRadius.circular(12),
                ),
                padding: const EdgeInsets.all(16),
                child: _buildDataTable(report),
              ),
            ),
            const SizedBox(height: 32),
          ],
        ),
      ),
    );
  }

  /// بناء بطاقة بتصميم عصري
  Widget _buildModernCard({
    required String title,
    required IconData icon,
    required Widget child,
    Color iconColor = Colors.blue,
    List<Widget>? actions,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(13), // 0.05 * 255 = ~13
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // عنوان البطاقة
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              border: Border(
                bottom: BorderSide(
                  color: Colors.grey.shade100,
                  width: 1,
                ),
              ),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  children: [
                    Icon(icon, color: iconColor),
                    const SizedBox(width: 12),
                    Text(
                      title,
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
                if (actions != null) Row(children: actions),
              ],
            ),
          ),
          // محتوى البطاقة
          Padding(
            padding: const EdgeInsets.all(16),
            child: child,
          ),
        ],
      ),
    );
  }

  /// بناء صف معلومات بتصميم عصري
  Widget _buildModernInfoRow({
    required IconData icon,
    required String label,
    required String value,
    Color iconColor = Colors.blue,
  }) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(icon, size: 18, color: iconColor),
          const SizedBox(width: 12),
          SizedBox(
            width: 100,
            child: Text(
              '$label:',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                color: Colors.grey.shade700,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(
                color: Colors.black87,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// بناء قائمة اختيار الأعمدة بتصميم عصري
  Widget _buildModernColumnSelector({
    required IconData icon,
    required String label,
    required String tableName,
    required String currentValue,
    required Function(String?) onChanged,
    bool allowEmpty = false,
    Color iconColor = Colors.blue,
  }) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Icon(icon, size: 18, color: iconColor),
        const SizedBox(width: 12),
        SizedBox(
          width: 120,
          child: Text(
            label,
            style: TextStyle(
              fontWeight: FontWeight.w500,
              color: Colors.grey.shade700,
            ),
          ),
        ),
        Expanded(
          child: Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.grey.shade300),
            ),
            padding: const EdgeInsets.symmetric(horizontal: 12),
            child: _buildColumnSelector(
              tableName,
              currentValue,
              onChanged,
              allowEmpty: allowEmpty,
            ),
          ),
        ),
      ],
    );
  }

  /// بناء زر اختيار نوع الرسم البياني بتصميم عصري
  Widget _buildModernChartTypeButton({
    required PowerBIChartType type,
    required IconData icon,
    required String label,
    required bool isSelected,
    required Color primaryColor,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: isSelected ? primaryColor : Colors.white,
        borderRadius: BorderRadius.circular(8),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(10),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(8),
          onTap: () {
            _powerBIController.updateChartType(type);
          },
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  icon,
                  color: isSelected ? Colors.white : primaryColor,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  label,
                  style: TextStyle(
                    color: isSelected ? Colors.white : Colors.black87,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// تحميل أعمدة الجدول مرة واحدة فقط
  Future<void> _loadTableColumns(String tableName) async {
    if (tableName.isEmpty) {
      debugPrint('خطأ: اسم الجدول فارغ');
      return;
    }

    // إذا كانت الأعمدة محملة بالفعل، لا تقم بتحميلها مرة أخرى
    if (_cachedColumns.containsKey(tableName) &&
        _cachedColumns[tableName]!.isNotEmpty) {
      debugPrint('استخدام الأعمدة المخزنة مؤقتًا للجدول: $tableName');
      return;
    }

    try {
      debugPrint('تحميل أعمدة الجدول: $tableName');

      // تحميل أعمدة الجدول من وحدة التحكم
      await _powerBIController.loadTableColumns(tableName);

      // الحصول على الأعمدة من وحدة التحكم
      final columns = _powerBIController.tableColumns[tableName] ?? [];

      // تحويل الأعمدة إلى قائمة من السلاسل النصية
      final columnNames = columns.map((col) => col['name'] as String).toList();

      // تخزين الأعمدة مؤقتًا
      _cachedColumns[tableName] = columnNames;

      debugPrint('تم تحميل ${columnNames.length} عمود للجدول: $tableName');
    } catch (e) {
      debugPrint('خطأ في تحميل أعمدة الجدول: $e');
      _cachedColumns[tableName] = [];
    }
  }

  /// بناء قائمة اختيار الأعمدة
  Widget _buildColumnSelector(
      String tableName, String currentValue, Function(String?) onChanged,
      {bool allowEmpty = false}) {
    // استخدام الأعمدة المخزنة مؤقتًا
    final columns = _cachedColumns[tableName] ?? [];

    if (columns.isEmpty) {
      return Row(
        children: [
          const Text('جاري تحميل الأعمدة...'),
          const SizedBox(width: 8),
          const SizedBox(
            width: 16,
            height: 16,
            child: CircularProgressIndicator(strokeWidth: 2),
          ),
        ],
      );
    }

    return DropdownButton<String>(
      value: currentValue.isEmpty
          ? (allowEmpty ? '' : null)
          : (columns.contains(currentValue) ? currentValue : null),
      hint: const Text('اختر عمود'),
      isExpanded: true,
      items: columns.isEmpty
          ? [
              if (allowEmpty)
                const DropdownMenuItem<String>(
                  value: '',
                  child: Text('بدون'),
                ),
              const DropdownMenuItem<String>(
                value: null,
                enabled: false,
                child: Text('لا توجد أعمدة متاحة'),
              ),
            ]
          : [
              if (allowEmpty)
                const DropdownMenuItem<String>(
                  value: '',
                  child: Text('بدون'),
                ),
              ...columns.map((column) => DropdownMenuItem<String>(
                    value: column,
                    child: Text(column),
                  )),
            ],
      onChanged: columns.isEmpty && !allowEmpty ? null : onChanged,
    );
  }

  /// بناء الرسم البياني
  Widget _buildChart(PowerBIReport report) {
    final chartData = _powerBIController.chartData;

    // طباعة معلومات تصحيحية
    debugPrint('بناء الرسم البياني لنوع: ${report.chartType}');
    debugPrint('مفاتيح البيانات المتاحة: ${chartData.keys.toList()}');

    if (_powerBIController.isLoading.value) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const CircularProgressIndicator(),
            const SizedBox(height: 16),
            Text(
              'جاري تحميل البيانات...',
              style: TextStyle(fontSize: 16, color: Colors.grey[700]),
            ),
          ],
        ),
      );
    }

    if (_powerBIController.errorMessage.value.isNotEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.error_outline, color: Colors.red, size: 48),
            const SizedBox(height: 16),
            Text(
              _powerBIController.errorMessage.value,
              style: const TextStyle(color: Colors.red, fontSize: 16),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton.icon(
              onPressed: _loadReport,
              icon: const Icon(Icons.refresh),
              label: const Text('إعادة المحاولة'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red[400],
                foregroundColor: Colors.white,
              ),
            ),
          ],
        ),
      );
    }

    if (chartData.isEmpty) {
      debugPrint('لا توجد بيانات للرسم البياني');
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.bar_chart_outlined, color: Colors.grey, size: 48),
            const SizedBox(height: 16),
            Text(
              'لا توجد بيانات للرسم البياني',
              style: TextStyle(fontSize: 16, color: Colors.grey[700]),
            ),
            const SizedBox(height: 16),
            ElevatedButton.icon(
              onPressed: _loadReport,
              icon: const Icon(Icons.refresh),
              label: const Text('تحديث البيانات'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.blue,
                foregroundColor: Colors.white,
              ),
            ),
          ],
        ),
      );
    }

    // التحقق من وجود رسالة خطأ
    if (chartData.containsKey('error')) {
      debugPrint('خطأ في البيانات: ${chartData['error']}');
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.warning_amber_rounded, color: Colors.orange, size: 48),
            const SizedBox(height: 16),
            Text(
              'خطأ في تحميل البيانات: ${chartData['error']}',
              style: const TextStyle(color: Colors.orange, fontSize: 16),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton.icon(
              onPressed: _loadReport,
              icon: const Icon(Icons.refresh),
              label: const Text('إعادة المحاولة'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.orange,
                foregroundColor: Colors.white,
              ),
            ),
          ],
        ),
      );
    }

    switch (report.chartType) {
      case PowerBIChartType.bar:
        final data = chartData['chartData'] as Map<String, double>? ?? {};
        debugPrint('بيانات الرسم البياني الشريطي: ${data.length} عناصر');
        if (data.isEmpty) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.bar_chart_outlined, color: Colors.grey, size: 48),
                const SizedBox(height: 16),
                const Text(
                  'لا توجد بيانات كافية للرسم البياني الشريطي',
                  style: TextStyle(fontSize: 16, color: Colors.grey),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 8),
                const Text(
                  'تأكد من اختيار الأعمدة المناسبة وتوفر البيانات',
                  style: TextStyle(fontSize: 14, color: Colors.grey),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          );
        }
        return SfCartesianChart(
          title: ChartTitle(text: report.title),
          primaryXAxis: CategoryAxis(),
          primaryYAxis: NumericAxis(),
          series: <CartesianSeries<dynamic, String>>[
            ColumnSeries<dynamic, String>(
              dataSource: const [],
              xValueMapper: (dynamic data, _) => data.toString(),
              yValueMapper: (dynamic data, _) => 0.0,
            )
          ],
        );

      case PowerBIChartType.line:
        // تحويل البيانات إلى التنسيق المطلوب لـ Syncfusion Charts
        final rawData = chartData['chartData'] as Map<String, dynamic>? ?? {};
        final Map<String, Map<String, double>> data = {};

        // تحويل البيانات من fl_chart format إلى Syncfusion format
        for (final entry in rawData.entries) {
          if (entry.value is List) {
            final Map<String, double> seriesData = {};
            final List<dynamic> spots = entry.value;

            for (int i = 0; i < spots.length; i++) {
              if (spots[i] is Map && spots[i].containsKey('x') && spots[i].containsKey('y')) {
                seriesData[spots[i]['x'].toString()] = spots[i]['y'].toDouble();
              } else {
                seriesData[i.toString()] = spots[i].toDouble();
              }
            }

            data[entry.key] = seriesData;
          }
        }
        debugPrint('بيانات الرسم البياني الخطي: ${data.length} سلاسل');
        if (data.isEmpty) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.show_chart, color: Colors.grey, size: 48),
                const SizedBox(height: 16),
                const Text(
                  'لا توجد بيانات كافية للرسم البياني الخطي',
                  style: TextStyle(fontSize: 16, color: Colors.grey),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 8),
                const Text(
                  'تأكد من اختيار الأعمدة المناسبة وتوفر البيانات الرقمية',
                  style: TextStyle(fontSize: 14, color: Colors.grey),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          );
        }

        // طباعة معلومات عن كل سلسلة
        data.forEach((key, seriesData) {
          debugPrint('السلسلة $key: ${seriesData.length} نقاط');
          if (seriesData.isNotEmpty) {
            final firstEntry = seriesData.entries.first;
            debugPrint('أول نقطة: (${firstEntry.key}, ${firstEntry.value})');
          }
        });

        return SfCartesianChart(
          title: ChartTitle(text: report.title),
          primaryXAxis: CategoryAxis(),
          primaryYAxis: NumericAxis(),
          series: <CartesianSeries<dynamic, String>>[
            LineSeries<dynamic, String>(
              dataSource: const [],
              xValueMapper: (dynamic data, _) => data.toString(),
              yValueMapper: (dynamic data, _) => 0.0,
            )
          ],
        );

      case PowerBIChartType.pie:
        final data = chartData['chartData'] as Map<String, double>? ?? {};
        debugPrint('بيانات الرسم البياني الدائري: ${data.length} عناصر');
        if (data.isEmpty) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.pie_chart_outline, color: Colors.grey, size: 48),
                const SizedBox(height: 16),
                const Text(
                  'لا توجد بيانات كافية للرسم البياني الدائري',
                  style: TextStyle(fontSize: 16, color: Colors.grey),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 8),
                const Text(
                  'تأكد من اختيار الأعمدة المناسبة وتوفر البيانات الرقمية الموجبة',
                  style: TextStyle(fontSize: 14, color: Colors.grey),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          );
        }
        return SfCircularChart(
          title: ChartTitle(text: report.title),
          legend: Legend(isVisible: true),
          series: <PieSeries<dynamic, String>>[
            PieSeries<dynamic, String>(
              dataSource: const [],
              xValueMapper: (dynamic data, _) => data.toString(),
              yValueMapper: (dynamic data, _) => 0.0,
            )
          ],
        );

      case PowerBIChartType.scatter:
      case PowerBIChartType.bubble:
        // تحويل البيانات إلى التنسيق المطلوب لـ Syncfusion Scatter Chart
        final rawSpots = chartData['spots'] as List<dynamic>? ?? [];
        final List<ScatterData> scatterData = [];

        for (final spot in rawSpots) {
          if (spot is Map && spot.containsKey('x') && spot.containsKey('y')) {
            scatterData.add(ScatterData(
              spot['x'].toDouble(),
              spot['y'].toDouble(),
            ));
          }
        }
        debugPrint('بيانات الرسم البياني النقطي: ${scatterData.length} نقاط');
        if (scatterData.isEmpty) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                    report.chartType == PowerBIChartType.scatter
                        ? Icons.scatter_plot
                        : Icons.bubble_chart,
                    color: Colors.grey,
                    size: 48),
                const SizedBox(height: 16),
                Text(
                  report.chartType == PowerBIChartType.scatter
                      ? 'لا توجد بيانات كافية للرسم البياني النقطي'
                      : 'لا توجد بيانات كافية للرسم البياني الفقاعي',
                  style: const TextStyle(fontSize: 16, color: Colors.grey),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 8),
                const Text(
                  'تأكد من اختيار الأعمدة المناسبة للمحورين X و Y وتوفر البيانات الرقمية',
                  style: TextStyle(fontSize: 14, color: Colors.grey),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          );
        }
        // استخدام مكون الرسم البياني النقطي من Syncfusion
        return SfCartesianChart(
          primaryXAxis: NumericAxis(
            title: AxisTitle(text: report.xAxisColumn),
          ),
          primaryYAxis: NumericAxis(
            title: AxisTitle(text: report.yAxisColumn),
          ),
          series: <CartesianSeries>[
            ScatterSeries<ScatterData, double>(
              dataSource: scatterData,
              xValueMapper: (ScatterData data, _) => data.x,
              yValueMapper: (ScatterData data, _) => data.y,
              color: Colors.blue,
              markerSettings: const MarkerSettings(
                isVisible: true,
                height: 8,
                width: 8,
              ),
            ),
          ],
          tooltipBehavior: TooltipBehavior(enable: true),
          zoomPanBehavior: ZoomPanBehavior(
            enablePinching: true,
            enablePanning: true,
            enableDoubleTapZooming: true,
          ),
        );

      case PowerBIChartType.table:
        return _buildDataTable(report);

      default:
        return const Center(
          child: Text('نوع الرسم البياني غير مدعوم'),
        );
    }
  }

  /// بناء جدول البيانات
  Widget _buildDataTable(PowerBIReport report) {
    // التعامل مع أنواع البيانات المختلفة
    List<Map<String, dynamic>> data = [];

    try {
      final rawData = _powerBIController.chartData['data'];

      if (rawData is List<Map<String, dynamic>>) {
        data = rawData;
      } else if (rawData is List) {
        // تحويل List<dynamic> إلى List<Map<String, dynamic>>
        data = rawData.map((item) {
          if (item is Map<String, dynamic>) {
            return item;
          } else if (item is Map) {
            // تحويل Map<dynamic, dynamic> إلى Map<String, dynamic>
            return item.map((key, value) => MapEntry(key.toString(), value));
          }
          return <String, dynamic>{};
        }).toList();
      }
    } catch (e) {
      debugPrint('خطأ في معالجة بيانات الجدول: $e');
    }

    debugPrint('بناء جدول البيانات: ${data.length} صفوف');
    if (data.isNotEmpty) {
      debugPrint('مفاتيح الصف الأول: ${data.first.keys.toList()}');
    }

    if (data.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.table_chart, color: Colors.grey, size: 48),
            const SizedBox(height: 16),
            const Text(
              'لا توجد بيانات للجدول',
              style: TextStyle(fontSize: 16, color: Colors.grey),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            const Text(
              'تأكد من اختيار الجدول المناسب وتوفر البيانات',
              style: TextStyle(fontSize: 14, color: Colors.grey),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton.icon(
              onPressed: _loadReport,
              icon: const Icon(Icons.refresh),
              label: const Text('تحديث البيانات'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.blue,
                foregroundColor: Colors.white,
              ),
            ),
          ],
        ),
      );
    }

    // تحديد الأعمدة التي سيتم عرضها
    List<String> displayColumns = [];

    // إذا كانت قائمة الأعمدة المحددة فارغة، استخدم جميع المفاتيح المتاحة في البيانات
    if (report.columnNames.isEmpty) {
      if (data.isNotEmpty) {
        displayColumns = data.first.keys.toList();
      }
    } else {
      // استخدم الأعمدة المحددة في التقرير
      displayColumns = report.columnNames;
    }

    // إذا كانت قائمة الأعمدة لا تزال فارغة، أضف عمودًا افتراضيًا
    if (displayColumns.isEmpty) {
      displayColumns = ['البيانات'];
    }

    // إنشاء أعمدة الجدول
    final columns = displayColumns.map((column) {
      return DataColumn(
        label: Text(
          column,
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
      );
    }).toList();

    debugPrint('تم إنشاء ${columns.length} أعمدة للجدول');

    // إنشاء صفوف الجدول
    List<DataRow> rows = [];

    if (displayColumns.length == 1 && displayColumns[0] == 'البيانات') {
      // إذا كان هناك عمود افتراضي واحد فقط، اعرض جميع البيانات في عمود واحد
      rows = data.map((row) {
        return DataRow(
          cells: [
            DataCell(
              Text(row.toString()),
            ),
          ],
        );
      }).toList();
    } else {
      // إنشاء صفوف الجدول باستخدام الأعمدة المحددة
      rows = data.map((row) {
        return DataRow(
          cells: displayColumns.map((column) {
            final value = row[column];
            return DataCell(
              Text(value?.toString() ?? ''),
            );
          }).toList(),
        );
      }).toList();
    }

    debugPrint('تم إنشاء ${rows.length} صفوف للجدول');

    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: DataTable(
        columns: columns,
        rows: rows.isEmpty
            ? [
                DataRow(
                    cells: List.generate(columns.length,
                        (index) => const DataCell(Text('لا توجد بيانات'))))
              ]
            : rows,
      ),
    );
  }

  /// تنسيق التاريخ
  String _formatDate(DateTime date) {
    return '${date.year}/${date.month}/${date.day} ${date.hour}:${date.minute}';
  }
}
