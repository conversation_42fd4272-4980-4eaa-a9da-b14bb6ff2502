import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../../constants/app_colors.dart';
import '../../../../controllers/task_controller.dart';
import '../../../../controllers/user_controller.dart';
import '../../../../controllers/department_controller.dart';
import '../base_chart_widget.dart';
import '../../models/dashboard_models.dart';

/// Numbers Widget - عرض الأرقام والإحصائيات من عدة مصادر بيانات
class NumbersWidget extends BaseChartWidget {
  final String? title;
  final String? subtitle;
  final List<String>? dataSources;
  final Map<String, dynamic>? config;

  const NumbersWidget({
    super.key,
    this.title,
    this.subtitle,
    this.dataSources,
    this.config,
    required super.item,
    super.filters,
    super.showHeader,
    super.showFilters,
    super.onRefresh,
  });

  @override
  Widget buildChartContent(BuildContext context, List<ChartData> data) {
    return NumbersContent(
      title: title,
      subtitle: subtitle,
      dataSources: dataSources ?? ['tasks', 'users', 'departments'],
      config: config,
    );
  }
}

/// محتوى Numbers Widget المنفصل
class NumbersContent extends StatefulWidget {
  final String? title;
  final String? subtitle;
  final List<String> dataSources;
  final Map<String, dynamic>? config;

  const NumbersContent({
    super.key,
    this.title,
    this.subtitle,
    required this.dataSources,
    this.config,
  });

  @override
  State<NumbersContent> createState() => _NumbersContentState();
}

class _NumbersContentState extends State<NumbersContent>
    with TickerProviderStateMixin {
  
  late final TaskController _taskController;
  late final UserController _userController;
  late final DepartmentController _departmentController;
  
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _fadeAnimation;
  
  Map<String, dynamic> _numbersData = {};
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _initializeControllers();
    _initializeAnimations();
    _loadData();
  }

  void _initializeControllers() {
    _taskController = Get.find<TaskController>();
    _userController = Get.find<UserController>();
    _departmentController = Get.find<DepartmentController>();
  }

  void _initializeAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );

    _scaleAnimation = Tween<double>(
      begin: 0.8,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.elasticOut,
    ));

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  Future<void> _loadData() async {
    setState(() => _isLoading = true);

    try {
      final data = <String, dynamic>{};

      for (final source in widget.dataSources) {
        switch (source) {
          case 'total_tasks':
            data['total_tasks'] = _taskController.allTasks.length;
            break;
          case 'completed_tasks':
            data['completed_tasks'] = _taskController.taskStats['completed'] ?? 0;
            break;
          case 'pending_tasks':
            data['pending_tasks'] = _taskController.taskStats['pending'] ?? 0;
            break;
          case 'overdue_tasks':
            data['overdue_tasks'] = _taskController.taskStats['overdue'] ?? 0;
            break;
          case 'total_users':
            data['total_users'] = _userController.users.length;
            break;
          case 'active_users':
            data['active_users'] = _userController.users.where((u) => u.isActive).length;
            break;
          case 'total_departments':
            data['total_departments'] = _departmentController.allDepartments.length;
            break;
          case 'completion_rate':
            final total = _taskController.taskStats['total'] ?? 0;
            final completed = _taskController.taskStats['completed'] ?? 0;
            data['completion_rate'] = total > 0 ? (completed / total * 100).round() : 0;
            break;
          case 'avg_tasks_per_user':
            final totalTasks = _taskController.allTasks.length;
            final totalUsers = _userController.users.length;
            data['avg_tasks_per_user'] = totalUsers > 0 ? (totalTasks / totalUsers).toStringAsFixed(1) : '0';
            break;
        }
      }

      setState(() {
        _numbersData = data;
        _isLoading = false;
      });

      _animationController.forward();
    } catch (e) {
      debugPrint('خطأ في تحميل بيانات Numbers Widget: $e');
      setState(() => _isLoading = false);
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildHeader(),
          const SizedBox(height: 16),
          Expanded(
            child: _isLoading ? _buildLoadingState() : _buildNumbersGrid(),
          ),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: AppColors.primary.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                Icons.numbers,
                color: AppColors.primary,
                size: 20,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    widget.title ?? 'إحصائيات',
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Colors.black87,
                    ),
                  ),
                  if (widget.subtitle != null) ...[
                    const SizedBox(height: 4),
                    Text(
                      widget.subtitle!,
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey.shade600,
                      ),
                    ),
                  ],
                ],
              ),
            ),
            IconButton(
              icon: const Icon(Icons.refresh, size: 20),
              onPressed: _loadData,
              tooltip: 'تحديث البيانات',
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildLoadingState() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(),
          SizedBox(height: 8),
          Text(
            'جاري تحميل البيانات...',
            style: TextStyle(fontSize: 12, color: Colors.grey),
          ),
        ],
      ),
    );
  }

  Widget _buildNumbersGrid() {
    if (_numbersData.isEmpty) {
      return const Center(
        child: Text(
          'لا توجد بيانات للعرض',
          style: TextStyle(color: Colors.grey),
        ),
      );
    }

    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return FadeTransition(
          opacity: _fadeAnimation,
          child: ScaleTransition(
            scale: _scaleAnimation,
            child: GridView.builder(
              gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 2,
                childAspectRatio: 1.5,
                crossAxisSpacing: 12,
                mainAxisSpacing: 12,
              ),
              itemCount: _numbersData.length,
              itemBuilder: (context, index) {
                final entry = _numbersData.entries.elementAt(index);
                return _buildNumberCard(entry.key, entry.value);
              },
            ),
          ),
        );
      },
    );
  }

  Widget _buildNumberCard(String key, dynamic value) {
    final config = _getNumberConfig(key);
    
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: config['color'].withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: config['color'].withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            config['icon'],
            color: config['color'],
            size: 24,
          ),
          const SizedBox(height: 8),
          Text(
            value.toString(),
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: config['color'],
            ),
          ),
          const SizedBox(height: 4),
          Text(
            config['label'],
            style: const TextStyle(
              fontSize: 10,
              color: Colors.black87,
            ),
            textAlign: TextAlign.center,
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }

  Map<String, dynamic> _getNumberConfig(String key) {
    switch (key) {
      case 'total_tasks':
        return {
          'label': 'إجمالي المهام',
          'icon': Icons.task_alt,
          'color': Colors.blue,
        };
      case 'completed_tasks':
        return {
          'label': 'المهام المكتملة',
          'icon': Icons.check_circle,
          'color': Colors.green,
        };
      case 'pending_tasks':
        return {
          'label': 'المهام المعلقة',
          'icon': Icons.pending,
          'color': Colors.orange,
        };
      case 'overdue_tasks':
        return {
          'label': 'المهام المتأخرة',
          'icon': Icons.warning,
          'color': Colors.red,
        };
      case 'total_users':
        return {
          'label': 'إجمالي المستخدمين',
          'icon': Icons.people,
          'color': Colors.purple,
        };
      case 'active_users':
        return {
          'label': 'المستخدمين النشطين',
          'icon': Icons.person,
          'color': Colors.teal,
        };
      case 'total_departments':
        return {
          'label': 'الأقسام',
          'icon': Icons.business,
          'color': Colors.indigo,
        };
      case 'completion_rate':
        return {
          'label': 'معدل الإكمال %',
          'icon': Icons.trending_up,
          'color': Colors.green,
        };
      case 'avg_tasks_per_user':
        return {
          'label': 'متوسط المهام/مستخدم',
          'icon': Icons.analytics,
          'color': Colors.amber,
        };
      default:
        return {
          'label': key,
          'icon': Icons.info,
          'color': Colors.grey,
        };
    }
  }
}
