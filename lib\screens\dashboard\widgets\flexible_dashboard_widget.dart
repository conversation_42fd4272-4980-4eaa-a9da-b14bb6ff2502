import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../constants/app_colors.dart';
import '../../../constants/app_styles.dart';
import '../models/dashboard_models.dart';
import 'chart_factory.dart';
import 'layout/grid_layout_manager.dart';
import 'customization/widget_customization_dialog.dart';

/// ويدجت الداشبورد المرن مع دعم Drag & Drop و Resize
/// يوفر تجربة تفاعلية متقدمة لتخصيص التخطيط
class FlexibleDashboardWidget extends StatefulWidget {
  final bool enableDragDrop;
  final bool enableResize;
  final bool showGridLines;
  final Function(List<DashboardItem>)? onLayoutChanged;

  const FlexibleDashboardWidget({
    super.key,
    this.enableDragDrop = true,
    this.enableResize = true,
    this.showGridLines = false,
    this.onLayoutChanged,
  });

  @override
  State<FlexibleDashboardWidget> createState() => _FlexibleDashboardWidgetState();
}

class _FlexibleDashboardWidgetState extends State<FlexibleDashboardWidget> {
  List<DashboardItem> _dashboardItems = [];
  bool _isEditMode = false;

  @override
  void initState() {
    super.initState();
    _initializeDashboardItems();
  }

  /// تهيئة عناصر الداشبورد الافتراضية
  void _initializeDashboardItems() {
    _dashboardItems = [
      // KPI Cards - صف أول
      _createDashboardItem(
        id: 'kpi_total_tasks',
        title: 'إجمالي المهام',
        chartType: ChartType.kpi,
        column: 0,
        row: 0,
        width: 3,
        height: 1,
      ),
      _createDashboardItem(
        id: 'kpi_completed_tasks',
        title: 'المهام المكتملة',
        chartType: ChartType.kpi,
        column: 3,
        row: 0,
        width: 3,
        height: 1,
      ),
      _createDashboardItem(
        id: 'kpi_pending_tasks',
        title: 'المهام المعلقة',
        chartType: ChartType.kpi,
        column: 6,
        row: 0,
        width: 3,
        height: 1,
      ),
      _createDashboardItem(
        id: 'kpi_users',
        title: 'المستخدمين النشطين',
        chartType: ChartType.kpi,
        column: 9,
        row: 0,
        width: 3,
        height: 1,
      ),

      // المخططات الرئيسية - صف ثاني
      _createDashboardItem(
        id: 'task_status_pie',
        title: 'توزيع المهام حسب الحالة',
        chartType: ChartType.pie,
        column: 0,
        row: 1,
        width: 4,
        height: 2,
      ),
      _createDashboardItem(
        id: 'task_completion_gauge',
        title: 'معدل إكمال المهام',
        chartType: ChartType.gauge,
        column: 4,
        row: 1,
        width: 4,
        height: 2,
      ),
      _createDashboardItem(
        id: 'monthly_tasks_bar',
        title: 'المهام الشهرية',
        chartType: ChartType.bar,
        column: 8,
        row: 1,
        width: 4,
        height: 2,
      ),

      // الويدجت المتقدمة - صف ثالث
      _createDashboardItem(
        id: 'calendar_tasks',
        title: 'تقويم المهام',
        chartType: ChartType.calendar,
        column: 0,
        row: 3,
        width: 6,
        height: 3,
      ),
      _createDashboardItem(
        id: 'numbers_stats',
        title: 'إحصائيات شاملة',
        chartType: ChartType.numbers,
        column: 6,
        row: 3,
        width: 3,
        height: 2,
      ),
      _createDashboardItem(
        id: 'battery_progress',
        title: 'مؤشر التقدم',
        chartType: ChartType.battery,
        column: 9,
        row: 3,
        width: 3,
        height: 2,
      ),

      // ويدجت إضافية - صف رابع
      _createDashboardItem(
        id: 'time_tracking',
        title: 'تتبع الوقت',
        chartType: ChartType.timeTracking,
        column: 6,
        row: 5,
        width: 3,
        height: 1,
      ),
      _createDashboardItem(
        id: 'workload_distribution',
        title: 'توزيع أعباء العمل',
        chartType: ChartType.workload,
        column: 9,
        row: 5,
        width: 3,
        height: 1,
      ),
    ];
  }

  /// إنشاء عنصر داشبورد
  DashboardItem _createDashboardItem({
    required String id,
    required String title,
    required ChartType chartType,
    required int column,
    required int row,
    required int width,
    required int height,
  }) {
    return DashboardItem(
      id: id,
      title: title,
      chartType: chartType,
      config: {
        'position': {'column': column, 'row': row},
        'size': {'width': width, 'height': height},
      },
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey.shade50,
      body: Column(
        children: [
          // شريط الأدوات
          _buildToolbar(),
          
          // الداشبورد المرن
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: GridLayoutManager(
                items: _dashboardItems,
                onLayoutChanged: _onLayoutChanged,
                itemBuilder: _buildDashboardItem,
                enableDragDrop: widget.enableDragDrop && _isEditMode,
                enableResize: widget.enableResize && _isEditMode,
                gridColumns: 12,
                itemHeight: 200,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// بناء شريط الأدوات
  Widget _buildToolbar() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withAlpha(25),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          // عنوان الداشبورد
          Text(
            'لوحة التحكم المرنة',
            style: AppStyles.headingLarge.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          
          const Spacer(),
          
          // زر وضع التحرير
          _buildEditModeToggle(),
          
          const SizedBox(width: 12),
          
          // زر إعادة تعيين التخطيط
          _buildResetLayoutButton(),
          
          const SizedBox(width: 12),
          
          // زر حفظ التخطيط
          _buildSaveLayoutButton(),
        ],
      ),
    );
  }

  /// زر تبديل وضع التحرير
  Widget _buildEditModeToggle() {
    return AnimatedContainer(
      duration: const Duration(milliseconds: 300),
      child: ElevatedButton.icon(
        onPressed: () {
          setState(() {
            _isEditMode = !_isEditMode;
          });
        },
        icon: Icon(_isEditMode ? Icons.check : Icons.edit),
        label: Text(_isEditMode ? 'إنهاء التحرير' : 'تحرير التخطيط'),
        style: ElevatedButton.styleFrom(
          backgroundColor: _isEditMode ? Colors.green : AppColors.primary,
          foregroundColor: Colors.white,
          elevation: _isEditMode ? 4 : 2,
        ),
      ),
    );
  }

  /// زر إعادة تعيين التخطيط
  Widget _buildResetLayoutButton() {
    return OutlinedButton.icon(
      onPressed: _isEditMode ? _resetLayout : null,
      icon: const Icon(Icons.refresh),
      label: const Text('إعادة تعيين'),
      style: OutlinedButton.styleFrom(
        foregroundColor: Colors.orange,
        side: BorderSide(color: Colors.orange.withAlpha(100)),
      ),
    );
  }

  /// زر حفظ التخطيط
  Widget _buildSaveLayoutButton() {
    return ElevatedButton.icon(
      onPressed: _isEditMode ? _saveLayout : null,
      icon: const Icon(Icons.save),
      label: const Text('حفظ التخطيط'),
      style: ElevatedButton.styleFrom(
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
      ),
    );
  }

  /// بناء عنصر واحد في الداشبورد
  Widget _buildDashboardItem(DashboardItem item) {
    Widget chart = ChartFactory.createChart(
      item: item,
      showHeader: true,
      showFilters: false,
    );

    // إضافة حدود وقائمة سياقية في وضع التحرير
    if (_isEditMode) {
      chart = GestureDetector(
        onSecondaryTapUp: (details) => _showContextMenu(context, details, item),
        child: Container(
          decoration: BoxDecoration(
            border: Border.all(
              color: AppColors.primary.withAlpha(100),
              width: 2,
              style: BorderStyle.solid,
            ),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Stack(
            children: [
              chart,
              // زر التخصيص في الزاوية
              Positioned(
                top: 8,
                right: 8,
                child: Container(
                  decoration: BoxDecoration(
                    color: AppColors.primary,
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: IconButton(
                    onPressed: () => _showCustomizationDialog(item),
                    icon: const Icon(
                      Icons.palette,
                      color: Colors.white,
                      size: 16,
                    ),
                    constraints: const BoxConstraints(
                      minWidth: 24,
                      minHeight: 24,
                    ),
                    padding: EdgeInsets.zero,
                  ),
                ),
              ),
            ],
          ),
        ),
      );
    }

    return chart;
  }

  /// إظهار القائمة السياقية
  void _showContextMenu(BuildContext context, TapUpDetails details, DashboardItem item) {
    showMenu(
      context: context,
      position: RelativeRect.fromLTRB(
        details.globalPosition.dx,
        details.globalPosition.dy,
        details.globalPosition.dx,
        details.globalPosition.dy,
      ),
      items: [
        PopupMenuItem(
          value: 'customize',
          child: const Row(
            children: [
              Icon(Icons.palette, size: 16),
              SizedBox(width: 8),
              Text('تخصيص المظهر'),
            ],
          ),
        ),
        PopupMenuItem(
          value: 'duplicate',
          child: const Row(
            children: [
              Icon(Icons.copy, size: 16),
              SizedBox(width: 8),
              Text('نسخ'),
            ],
          ),
        ),
        PopupMenuItem(
          value: 'delete',
          child: const Row(
            children: [
              Icon(Icons.delete, size: 16, color: Colors.red),
              SizedBox(width: 8),
              Text('حذف', style: TextStyle(color: Colors.red)),
            ],
          ),
        ),
      ],
    ).then((value) {
      switch (value) {
        case 'customize':
          _showCustomizationDialog(item);
          break;
        case 'duplicate':
          _duplicateWidget(item);
          break;
        case 'delete':
          _deleteWidget(item);
          break;
      }
    });
  }

  /// إظهار حوار التخصيص
  void _showCustomizationDialog(DashboardItem item) {
    showDialog(
      context: context,
      builder: (context) => WidgetCustomizationDialog(
        item: item,
        onSave: (updatedItem) {
          setState(() {
            final index = _dashboardItems.indexWhere((i) => i.id == item.id);
            if (index != -1) {
              _dashboardItems[index] = updatedItem;
            }
          });
          _showSuccessMessage('تم حفظ التخصيص بنجاح');
        },
      ),
    );
  }

  /// نسخ الـ widget
  void _duplicateWidget(DashboardItem item) {
    final newItem = item.copyWith(
      id: '${item.id}_copy_${DateTime.now().millisecondsSinceEpoch}',
      title: '${item.title} (نسخة)',
      config: {
        ...item.config,
        'position': {
          'column': 0,
          'row': _getMaxRow() + 1,
        },
      },
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );

    setState(() {
      _dashboardItems.add(newItem);
    });
    _showSuccessMessage('تم نسخ الـ Widget بنجاح');
  }

  /// حذف الـ widget
  void _deleteWidget(DashboardItem item) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('حذف Widget'),
        content: Text('هل أنت متأكد من حذف "${item.title}"؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              setState(() {
                _dashboardItems.removeWhere((i) => i.id == item.id);
              });
              _showSuccessMessage('تم حذف الـ Widget بنجاح');
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('حذف'),
          ),
        ],
      ),
    );
  }

  /// الحصول على أقصى صف
  int _getMaxRow() {
    if (_dashboardItems.isEmpty) return 0;
    return _dashboardItems
        .map((item) => (item.config['position'] as Map<String, dynamic>?)?['row'] ?? 0)
        .reduce((a, b) => a > b ? a : b);
  }

  /// معالج تغيير التخطيط
  void _onLayoutChanged(List<DashboardItem> items) {
    setState(() {
      _dashboardItems = items;
    });
    
    widget.onLayoutChanged?.call(items);
  }

  /// إعادة تعيين التخطيط للافتراضي
  void _resetLayout() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('إعادة تعيين التخطيط'),
        content: const Text('هل أنت متأكد من إعادة تعيين التخطيط إلى الوضع الافتراضي؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              setState(() {
                _initializeDashboardItems();
              });
              _showSuccessMessage('تم إعادة تعيين التخطيط بنجاح');
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.orange),
            child: const Text('إعادة تعيين'),
          ),
        ],
      ),
    );
  }

  /// حفظ التخطيط الحالي
  void _saveLayout() {
    // هنا يمكن إضافة منطق حفظ التخطيط في قاعدة البيانات
    _showSuccessMessage('تم حفظ التخطيط بنجاح');
  }

  /// إظهار رسالة نجاح
  void _showSuccessMessage(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.green,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
    );
  }
}
