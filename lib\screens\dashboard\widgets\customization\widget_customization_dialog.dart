import 'package:flutter/material.dart';
import '../../../../constants/app_colors.dart';
import '../../../../constants/app_styles.dart';
import '../../models/dashboard_models.dart';

/// حوار تخصيص مظهر الـ Widget
/// يسمح بتغيير الألوان، الحجم، والإعدادات الأخرى
class WidgetCustomizationDialog extends StatefulWidget {
  final DashboardItem item;
  final Function(DashboardItem) onSave;

  const WidgetCustomizationDialog({
    super.key,
    required this.item,
    required this.onSave,
  });

  @override
  State<WidgetCustomizationDialog> createState() => _WidgetCustomizationDialogState();
}

class _WidgetCustomizationDialogState extends State<WidgetCustomizationDialog> {
  late DashboardItem _editedItem;
  late TextEditingController _titleController;
  
  // خيارات التخصيص
  Color _primaryColor = AppColors.primary;
  Color _backgroundColor = Colors.white;
  bool _showBorder = true;
  double _borderRadius = 12.0;
  bool _showShadow = true;
  double _shadowElevation = 2.0;

  @override
  void initState() {
    super.initState();
    _editedItem = widget.item;
    _titleController = TextEditingController(text: widget.item.title);
    _loadCurrentSettings();
  }

  @override
  void dispose() {
    _titleController.dispose();
    super.dispose();
  }

  /// تحميل الإعدادات الحالية
  void _loadCurrentSettings() {
    final customization = _editedItem.config['customization'] as Map<String, dynamic>?;
    if (customization != null) {
      _primaryColor = Color(customization['primaryColor'] ?? AppColors.primary.value);
      _backgroundColor = Color(customization['backgroundColor'] ?? Colors.white.value);
      _showBorder = customization['showBorder'] ?? true;
      _borderRadius = customization['borderRadius']?.toDouble() ?? 12.0;
      _showShadow = customization['showShadow'] ?? true;
      _shadowElevation = customization['shadowElevation']?.toDouble() ?? 2.0;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Container(
        width: MediaQuery.of(context).size.width * 0.9,
        constraints: const BoxConstraints(
          maxWidth: 600,
          maxHeight: 700,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // رأس الحوار
            _buildDialogHeader(),
            
            // محتوى التخصيص
            Expanded(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(20),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildBasicSettings(),
                    const SizedBox(height: 24),
                    _buildColorSettings(),
                    const SizedBox(height: 24),
                    _buildAppearanceSettings(),
                    const SizedBox(height: 24),
                    _buildPreview(),
                  ],
                ),
              ),
            ),
            
            // أزرار الإجراءات
            _buildActionButtons(),
          ],
        ),
      ),
    );
  }

  /// بناء رأس الحوار
  Widget _buildDialogHeader() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: AppColors.primary,
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(16),
          topRight: Radius.circular(16),
        ),
      ),
      child: Row(
        children: [
          Icon(
            widget.item.chartType.icon,
            color: Colors.white,
            size: 24,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              'تخصيص ${widget.item.chartType.displayName}',
              style: AppStyles.titleMedium.copyWith(
                color: Colors.white,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          IconButton(
            onPressed: () => Navigator.of(context).pop(),
            icon: const Icon(Icons.close, color: Colors.white),
          ),
        ],
      ),
    );
  }

  /// بناء الإعدادات الأساسية
  Widget _buildBasicSettings() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'الإعدادات الأساسية',
          style: AppStyles.titleMedium.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        
        // عنوان الـ Widget
        TextField(
          controller: _titleController,
          decoration: const InputDecoration(
            labelText: 'عنوان الـ Widget',
            border: OutlineInputBorder(),
            prefixIcon: Icon(Icons.title),
          ),
          onChanged: (value) {
            setState(() {
              _editedItem = _editedItem.copyWith(title: value);
            });
          },
        ),
      ],
    );
  }

  /// بناء إعدادات الألوان
  Widget _buildColorSettings() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'الألوان',
          style: AppStyles.titleMedium.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        
        // اللون الأساسي
        _buildColorPicker(
          'اللون الأساسي',
          _primaryColor,
          (color) => setState(() => _primaryColor = color),
        ),
        
        const SizedBox(height: 16),
        
        // لون الخلفية
        _buildColorPicker(
          'لون الخلفية',
          _backgroundColor,
          (color) => setState(() => _backgroundColor = color),
        ),
      ],
    );
  }

  /// بناء منتقي الألوان
  Widget _buildColorPicker(String label, Color currentColor, Function(Color) onColorChanged) {
    return Row(
      children: [
        Expanded(
          child: Text(label),
        ),
        GestureDetector(
          onTap: () => _showColorPicker(currentColor, onColorChanged),
          child: Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: currentColor,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.grey.shade300),
            ),
          ),
        ),
      ],
    );
  }

  /// إظهار منتقي الألوان
  void _showColorPicker(Color currentColor, Function(Color) onColorChanged) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('اختر اللون'),
        content: SingleChildScrollView(
          child: BlockPicker(
            pickerColor: currentColor,
            onColorChanged: onColorChanged,
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('تم'),
          ),
        ],
      ),
    );
  }

  /// بناء إعدادات المظهر
  Widget _buildAppearanceSettings() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'المظهر',
          style: AppStyles.titleMedium.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        
        // إظهار الحدود
        SwitchListTile(
          title: const Text('إظهار الحدود'),
          value: _showBorder,
          onChanged: (value) => setState(() => _showBorder = value),
        ),
        
        // نصف قطر الحدود
        if (_showBorder) ...[
          const SizedBox(height: 8),
          Text('نصف قطر الحدود: ${_borderRadius.toInt()}'),
          Slider(
            value: _borderRadius,
            min: 0,
            max: 30,
            divisions: 30,
            onChanged: (value) => setState(() => _borderRadius = value),
          ),
        ],
        
        // إظهار الظل
        SwitchListTile(
          title: const Text('إظهار الظل'),
          value: _showShadow,
          onChanged: (value) => setState(() => _showShadow = value),
        ),
        
        // ارتفاع الظل
        if (_showShadow) ...[
          const SizedBox(height: 8),
          Text('ارتفاع الظل: ${_shadowElevation.toInt()}'),
          Slider(
            value: _shadowElevation,
            min: 0,
            max: 10,
            divisions: 10,
            onChanged: (value) => setState(() => _shadowElevation = value),
          ),
        ],
      ],
    );
  }

  /// بناء معاينة التخصيص
  Widget _buildPreview() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'معاينة',
          style: AppStyles.titleMedium.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        
        Container(
          width: double.infinity,
          height: 120,
          decoration: BoxDecoration(
            color: _backgroundColor,
            borderRadius: BorderRadius.circular(_borderRadius),
            border: _showBorder ? Border.all(color: _primaryColor, width: 2) : null,
            boxShadow: _showShadow ? [
              BoxShadow(
                color: Colors.grey.withAlpha(50),
                blurRadius: _shadowElevation * 2,
                offset: Offset(0, _shadowElevation),
              ),
            ] : null,
          ),
          child: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  widget.item.chartType.icon,
                  color: _primaryColor,
                  size: 32,
                ),
                const SizedBox(height: 8),
                Text(
                  _titleController.text,
                  style: TextStyle(
                    color: _primaryColor,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  /// بناء أزرار الإجراءات
  Widget _buildActionButtons() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: const BorderRadius.only(
          bottomLeft: Radius.circular(16),
          bottomRight: Radius.circular(16),
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
          const SizedBox(width: 12),
          ElevatedButton(
            onPressed: _saveCustomization,
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primary,
              foregroundColor: Colors.white,
            ),
            child: const Text('حفظ التخصيص'),
          ),
        ],
      ),
    );
  }

  /// حفظ التخصيص
  void _saveCustomization() {
    final customization = {
      'primaryColor': _primaryColor.value,
      'backgroundColor': _backgroundColor.value,
      'showBorder': _showBorder,
      'borderRadius': _borderRadius,
      'showShadow': _showShadow,
      'shadowElevation': _shadowElevation,
    };

    final updatedItem = _editedItem.copyWith(
      title: _titleController.text,
      config: {
        ..._editedItem.config,
        'customization': customization,
      },
      updatedAt: DateTime.now(),
    );

    widget.onSave(updatedItem);
    Navigator.of(context).pop();
  }
}

/// منتقي الألوان البسيط
class BlockPicker extends StatelessWidget {
  final Color pickerColor;
  final Function(Color) onColorChanged;

  const BlockPicker({
    super.key,
    required this.pickerColor,
    required this.onColorChanged,
  });

  @override
  Widget build(BuildContext context) {
    final colors = [
      Colors.red, Colors.pink, Colors.purple, Colors.deepPurple,
      Colors.indigo, Colors.blue, Colors.lightBlue, Colors.cyan,
      Colors.teal, Colors.green, Colors.lightGreen, Colors.lime,
      Colors.yellow, Colors.amber, Colors.orange, Colors.deepOrange,
      Colors.brown, Colors.grey, Colors.blueGrey, Colors.black,
    ];

    return Wrap(
      children: colors.map((color) => GestureDetector(
        onTap: () => onColorChanged(color),
        child: Container(
          width: 40,
          height: 40,
          margin: const EdgeInsets.all(4),
          decoration: BoxDecoration(
            color: color,
            borderRadius: BorderRadius.circular(8),
            border: Border.all(
              color: pickerColor == color ? Colors.black : Colors.transparent,
              width: 2,
            ),
          ),
        ),
      )).toList(),
    );
  }
}
