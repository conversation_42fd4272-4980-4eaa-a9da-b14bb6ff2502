import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../constants/app_colors.dart';
import '../../../constants/app_styles.dart';
import '../../../controllers/task_controller.dart';
import '../../../controllers/user_controller.dart';
import '../../../controllers/department_controller.dart';
import '../../../controllers/auth_controller.dart';
import 'charts/numbers_widget.dart';
import 'charts/battery_widget.dart';
import 'charts/time_tracking_widget.dart';
import 'charts/workload_widget.dart';
import 'charts/calendar_widget.dart';
import '../models/dashboard_models.dart';
import 'chart_factory.dart';

/// كلاس بيانات المخطط
class ChartSampleData {
  final String x;
  final double y;
  final Color color;

  ChartSampleData(this.x, this.y, this.color);
}

/// لوحة تحكم ذكية وعصرية تضم جميع بيانات قاعدة البيانات
class SmartDashboardWidget extends StatefulWidget {
  const SmartDashboardWidget({super.key});

  @override
  State<SmartDashboardWidget> createState() => _SmartDashboardWidgetState();
}

class _SmartDashboardWidgetState extends State<SmartDashboardWidget> {
  
  // Controllers
  late final TaskController _taskController;
  late final UserController _userController;
  late final DepartmentController _departmentController;
  late final AuthController _authController;
  // تم إزالة _permissionService غير المستخدم
  
  // تم إزالة Animation Controllers لتبسيط الكود
  
  // State
  bool _isLoading = true;
  String _selectedTimeFilter = 'month';
  
  @override
  void initState() {
    super.initState();
    _initializeControllers();
    _loadData();
  }
  
  void _initializeControllers() {
    _taskController = Get.find<TaskController>();
    _userController = Get.find<UserController>();
    _departmentController = Get.find<DepartmentController>();
    _authController = Get.find<AuthController>();
    // تم إزالة _permissionService غير المستخدم
  }
  
  // تم إزالة _initializeAnimations
  
  Future<void> _loadData() async {
    setState(() => _isLoading = true);
    
    try {
      // تحميل البيانات من جميع Controllers
      await Future.wait([
        _taskController.loadAllTasks(forceRefresh: true),
        _userController.loadAllUsers(),
        _departmentController.loadAllDepartments(forceRefresh: true),
      ]);
      
      // تم إزالة الرسوم المتحركة
      
    } catch (e) {
      debugPrint('خطأ في تحميل بيانات لوحة التحكم: $e');
    } finally {
      setState(() => _isLoading = false);
    }
  }
  
  @override
  void dispose() {
    // تم إزالة animation controllers
    super.dispose();
  }
  
  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return _buildLoadingWidget();
    }

    return RefreshIndicator(
      onRefresh: _loadData,
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        physics: const AlwaysScrollableScrollPhysics(),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
                _buildHeader(),
                const SizedBox(height: 24),
                _buildKPICards(),
                const SizedBox(height: 24),
                _buildAdvancedWidgets(),
                const SizedBox(height: 24),
                _buildMainCharts(),
                const SizedBox(height: 24),
                _buildDetailedAnalytics(),
                const SizedBox(height: 24),
                _buildSystemHealth(),
                const SizedBox(height: 100), // مساحة إضافية في النهاية
          ],
        ),
      ),
    );
  }
  
  Widget _buildLoadingWidget() {
    return SizedBox(
      height: MediaQuery.of(context).size.height * 0.7,
      child: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text(
              'جاري تحميل لوحة التحكم الذكية...',
              style: TextStyle(fontSize: 16, color: Colors.grey),
            ),
          ],
        ),
      ),
    );
  }
  
  Widget _buildHeader() {
    final currentUser = _authController.currentUser.value;
    final currentTime = DateTime.now();
    final greeting = _getGreeting(currentTime.hour);
    
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            AppColors.primary,
            AppColors.primary.withAlpha(80),
          ],
        ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: AppColors.primary.withAlpha(76),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  '$greeting ${currentUser?.name ?? 'المستخدم'}',
                  style: const TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'مرحباً بك في لوحة التحكم الذكية',
                  style: TextStyle(
                    fontSize: 16,
                    color: Colors.white.withAlpha(230),
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  _formatDate(currentTime),
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.white.withAlpha(204),
                  ),
                ),
              ],
            ),
          ),
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.white.withAlpha(51),
              borderRadius: BorderRadius.circular(12),
            ),
            child: const Icon(
              Icons.dashboard,
              size: 32,
              color: Colors.white,
            ),
          ),
        ],
      ),
    );
  }
  
  String _getGreeting(int hour) {
    if (hour < 12) return 'صباح الخير';
    if (hour < 17) return 'مساء الخير';
    return 'مساء الخير';
  }
  
  String _formatDate(DateTime date) {
    const months = [
      'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
      'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
    ];
    
    const weekdays = [
      'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت', 'الأحد'
    ];
    
    return '${weekdays[date.weekday - 1]}، ${date.day} ${months[date.month - 1]} ${date.year}';
  }
  
  Widget _buildKPICards() {
    final taskStats = _taskController.taskStats;
    final totalUsers = _userController.users.length;
    final totalDepartments = _departmentController.allDepartments.length;
    final totalTasks = taskStats['total'] ?? 0;
    final completedTasks = taskStats['completed'] ?? 0;
    final completionRate = totalTasks > 0 ? (completedTasks / totalTasks * 100) : 0.0;
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'المؤشرات الرئيسية',
          style: AppStyles.headingLarge.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        GridView.count(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          crossAxisCount: 4, // زيادة عدد الأعمدة
          childAspectRatio: 2.0, // نسبة أفضل للبطاقات الصغيرة
          crossAxisSpacing: 12,
          mainAxisSpacing: 12,
          children: [
            _buildKPICard(
              title: 'إجمالي المهام',
              value: totalTasks.toString(),
              icon: Icons.task_alt,
              color: Colors.blue,
              trend: '+12%',
              isPositive: true,
            ),
            _buildKPICard(
              title: 'معدل الإكمال',
              value: '${completionRate.toInt()}%',
              icon: Icons.trending_up,
              color: Colors.green,
              trend: '+5%',
              isPositive: true,
            ),
            _buildKPICard(
              title: 'المستخدمين النشطين',
              value: totalUsers.toString(),
              icon: Icons.people,
              color: Colors.orange,
              trend: '+3%',
              isPositive: true,
            ),
            _buildKPICard(
              title: 'الأقسام',
              value: totalDepartments.toString(),
              icon: Icons.business,
              color: Colors.purple,
              trend: '0%',
              isPositive: null,
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildKPICard({
    required String title,
    required String value,
    required IconData icon,
    required Color color,
    required String trend,
    bool? isPositive,
  }) {
    return Container(
      padding: const EdgeInsets.all(12), // تقليل padding
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withAlpha(25),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Container(
                padding: const EdgeInsets.all(6), // تقليل padding
                decoration: BoxDecoration(
                  color: color.withAlpha(25),
                  borderRadius: BorderRadius.circular(6),
                ),
                child: Icon(icon, color: color, size: 18), // تقليل حجم الأيقونة
              ),
              if (isPositive != null)
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                  decoration: BoxDecoration(
                    color: isPositive ? Colors.green.withAlpha(25) : Colors.red.withAlpha(25),
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        isPositive ? Icons.arrow_upward : Icons.arrow_downward,
                        size: 12,
                        color: isPositive ? Colors.green : Colors.red,
                      ),
                      const SizedBox(width: 2),
                      Text(
                        trend,
                        style: TextStyle(
                          fontSize: 10,
                          color: isPositive ? Colors.green : Colors.red,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                ),
            ],
          ),
          const Spacer(),
          Text(
            value,
            style: const TextStyle(
              fontSize: 20, // تقليل حجم الرقم
              fontWeight: FontWeight.bold,
              color: Colors.black87,
            ),
          ),
          const SizedBox(height: 2), // تقليل المسافة
          Text(
            title,
            style: TextStyle(
              fontSize: 11, // تقليل حجم النص
              color: Colors.grey.shade600,
            ),
          ),
        ],
      ),
    );
  }

  /// بناء الـ Widgets المتقدمة الجديدة
  Widget _buildAdvancedWidgets() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'الـ Widgets المتقدمة',
          style: AppStyles.headingLarge.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        GridView.count(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          crossAxisCount: 3, // زيادة عدد الأعمدة
          childAspectRatio: 1.5, // نسبة أفضل
          crossAxisSpacing: 12,
          mainAxisSpacing: 12,
          children: [
            // Numbers Widget
            NumbersWidget(
              item: DashboardItem(
                id: 'numbers_stats',
                title: 'إحصائيات المهام',
                chartType: ChartType.numbers,
                createdAt: DateTime.now(),
                updatedAt: DateTime.now(),
              ),
              title: 'إحصائيات المهام',
              subtitle: 'أرقام شاملة من النظام',
              dataSources: const [
                'total_tasks',
                'completed_tasks',
                'pending_tasks',
                'overdue_tasks',
              ],
              ),
            
            // Battery Widget
            BatteryWidget(
              item: DashboardItem(
                id: 'battery_completion',
                title: 'مؤشر الإكمال',
                chartType: ChartType.battery,
                createdAt: DateTime.now(),
                updatedAt: DateTime.now(),
              ),
              title: 'مؤشر الإكمال',
              subtitle: 'نسبة إكمال المهام',
              dataSource: 'task_completion',
            ),
            // Time Tracking Widget
            TimeTrackingWidget(
              item: DashboardItem(
                id: 'time_tracking',
                title: 'تتبع الوقت',
                chartType: ChartType.timeTracking,
                createdAt: DateTime.now(),
                updatedAt: DateTime.now(),
              ),
              title: 'تتبع الوقت',
              subtitle: 'الوقت المستغرق في المهام',
            ),
            // Workload Widget
            WorkloadWidget(
              item: DashboardItem(
                id: 'workload_distribution',
                title: 'عبء العمل',
                chartType: ChartType.workload,
                createdAt: DateTime.now(),
                updatedAt: DateTime.now(),
              ),
              title: 'عبء العمل',
              subtitle: 'توزيع المهام على الفريق',
            ),
            // Numbers Widget للمستخدمين
            NumbersWidget(
              item: DashboardItem(
                id: 'numbers_users',
                title: 'إحصائيات المستخدمين',
                chartType: ChartType.numbers,
                createdAt: DateTime.now(),
                updatedAt: DateTime.now(),
              ),
              title: 'إحصائيات المستخدمين',
              subtitle: 'بيانات الفريق',
              dataSources: const [
                'total_users',
                'active_users',
                'total_departments',
                'avg_tasks_per_user',
              ],
            ),
            // Calendar Widget
            CalendarWidget(
              title: 'تقويم المهام',
              subtitle: 'عرض المهام حسب التاريخ',
              item: DashboardItem(
                id: 'calendar_tasks',
                title: 'تقويم المهام',
                chartType: ChartType.calendar,
                createdAt: DateTime.now(),
                updatedAt: DateTime.now(),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildMainCharts() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'التحليلات الرئيسية',
              style: AppStyles.headingLarge.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            _buildTimeFilterDropdown(),
          ],
        ),
        const SizedBox(height: 16),
        GridView.count(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          crossAxisCount: 3, // زيادة عدد الأعمدة
          childAspectRatio: 1.3, // نسبة أفضل
          crossAxisSpacing: 12,
          mainAxisSpacing: 12,
          children: [
            // مخطط توزيع المهام حسب الحالة
            ChartFactory.createChart(
              item: DashboardItem(
                id: 'task_status',
                title: 'توزيع المهام حسب الحالة',
                chartType: ChartType.pie,
                createdAt: DateTime.now(),
                updatedAt: DateTime.now(),
              ),
            ),
            // مقياس معدل الإكمال
            ChartFactory.createChart(
              item: DashboardItem(
                id: 'task_completion',
                title: 'معدل إكمال المهام',
                chartType: ChartType.gauge,
                createdAt: DateTime.now(),
                updatedAt: DateTime.now(),
              ),
            ),
            // مخطط المهام الشهرية
            ChartFactory.createChart(
              item: DashboardItem(
                id: 'monthly_tasks',
                title: 'المهام الشهرية',
                chartType: ChartType.bar,
                createdAt: DateTime.now(),
                updatedAt: DateTime.now(),
              ),
            ),
            // مخطط أداء المستخدمين
            ChartFactory.createChart(
              item: DashboardItem(
                id: 'user_performance',
                title: 'أداء المستخدمين',
                chartType: ChartType.bar,
                createdAt: DateTime.now(),
                updatedAt: DateTime.now(),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildTimeFilterDropdown() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade300),
      ),
      child: DropdownButtonHideUnderline(
        child: DropdownButton<String>(
          value: _selectedTimeFilter,
          items: const [
            DropdownMenuItem(value: 'week', child: Text('هذا الأسبوع')),
            DropdownMenuItem(value: 'month', child: Text('هذا الشهر')),
            DropdownMenuItem(value: 'quarter', child: Text('هذا الربع')),
            DropdownMenuItem(value: 'year', child: Text('هذا العام')),
          ],
          onChanged: (value) {
            if (value != null) {
              setState(() => _selectedTimeFilter = value);
            }
          },
          style: const TextStyle(fontSize: 14, color: Colors.black87),
        ),
      ),
    );
  }

  // تم نقل هذه الدالة إلى ChartFactory - لا تحذف التعليق للمرجعية

  // تم نقل هذه الدالة إلى ChartFactory

  // تم نقل هذه الدالة إلى ChartFactory

  // تم نقل هذه الدالة إلى ChartFactory

  // تم نقل هذه الدالة إلى BaseChartWidget

  Widget _buildDetailedAnalytics() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'التحليلات التفصيلية',
          style: AppStyles.headingLarge.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        Row(
          children: [
            // مخطط توزيع المهام حسب الأقسام
            Expanded(
              child: ChartFactory.createChart(
                item: DashboardItem(
                  id: 'department_distribution',
                  title: 'توزيع المهام حسب الأقسام',
                  chartType: ChartType.bar,
                  config: {
                    'dataType': 'department_distribution',
                    'groupBy': 'department',
                  },
                  createdAt: DateTime.now(),
                  updatedAt: DateTime.now(),
                ),
              ),
            ),
            const SizedBox(width: 16),
            // مخطط توزيع المهام حسب الأولوية
            Expanded(
              child: ChartFactory.createChart(
                item: DashboardItem(
                  id: 'priority_distribution',
                  title: 'توزيع المهام حسب الأولوية',
                  chartType: ChartType.pie,
                  config: {
                    'dataType': 'priority_distribution',
                    'groupBy': 'priority',
                  },
                  createdAt: DateTime.now(),
                  updatedAt: DateTime.now(),
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        // الجدول الزمني للمهام
        ChartFactory.createChart(
          item: DashboardItem(
            id: 'task_timeline',
            title: 'الجدول الزمني للمهام',
            chartType: ChartType.line,
            config: {
              'dataType': 'task_timeline',
              'groupBy': 'date',
              'timeRange': 'last_30_days',
            },
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
          ),
        ),
      ],
    );
  }

  Widget _buildSystemHealth() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'صحة النظام',
          style: AppStyles.headingLarge.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        Row(
          children: [
            Expanded(child: _buildSystemMetrics()),
            const SizedBox(width: 16),
            Expanded(child: _buildRecentActivity()),
          ],
        ),
      ],
    );
  }

  // تم نقل هذه الدالة إلى ChartFactory

  // تم نقل هذه الدالة إلى ChartFactory

  // تم نقل هذه الدالة إلى ChartFactory

  Widget _buildSystemMetrics() {
    final totalUsers = _userController.users.length;
    final activeUsers = _userController.users.where((u) => u.isActive).length;
    final totalTasks = _taskController.allTasks.length;

    return Container(
      padding: const EdgeInsets.all(12), // تقليل padding
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withAlpha(25),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          const Text(
            'مقاييس النظام',
            style: TextStyle(
              fontSize: 13, // تقليل حجم النص
              fontWeight: FontWeight.bold,
              color: Colors.black87,
            ),
          ),
          const SizedBox(height: 8), // تقليل المسافة
          _buildMetricRow('المستخدمين النشطين', '$activeUsers / $totalUsers'),
          _buildMetricRow('إجمالي المهام', totalTasks.toString()),
          _buildMetricRow('متوسط المهام/مستخدم',
            totalUsers > 0 ? (totalTasks / totalUsers).toStringAsFixed(1) : '0'),
        ],
      ),
    );
  }

  Widget _buildRecentActivity() {
    return Container(
      padding: const EdgeInsets.all(12), // تقليل padding
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withAlpha(25),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          const Text(
            'النشاط الأخير',
            style: TextStyle(
              fontSize: 13, // تقليل حجم النص
              fontWeight: FontWeight.bold,
              color: Colors.black87,
            ),
          ),
          const SizedBox(height: 8), // تقليل المسافة
          const ListTile(
            leading: Icon(Icons.add_task, color: Colors.green),
            title: Text('تم إنشاء مهمة جديدة'),
            subtitle: Text('منذ 5 دقائق'),
            dense: true,
          ),
          const ListTile(
            leading: Icon(Icons.check_circle, color: Colors.blue),
            title: Text('تم إكمال مهمة'),
            subtitle: Text('منذ 15 دقيقة'),
            dense: true,
          ),
          const ListTile(
            leading: Icon(Icons.person_add, color: Colors.orange),
            title: Text('تم إضافة مستخدم جديد'),
            subtitle: Text('منذ ساعة'),
            dense: true,
          ),
        ],
      ),
    );
  }

  Widget _buildMetricRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(label, style: const TextStyle(fontSize: 14)),
          Text(value, style: const TextStyle(fontSize: 14, fontWeight: FontWeight.bold)),
        ],
      ),
    );
  }

  // تم نقل هذه الدوال إلى Chart Widgets المنفصلة
}
