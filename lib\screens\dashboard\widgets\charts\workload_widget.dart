import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:syncfusion_flutter_charts/charts.dart';
import '../../../../constants/app_colors.dart';
import '../../../../controllers/task_controller.dart';
import '../../../../controllers/user_controller.dart';
import '../base_chart_widget.dart';
import '../../models/dashboard_models.dart';

/// Workload Widget - تتبع أعباء العمل للفريق وتوزيع المهام
class WorkloadWidget extends BaseChartWidget {
  final String? title;
  final String? subtitle;
  final Map<String, dynamic>? config;

  const WorkloadWidget({
    super.key,
    this.title,
    this.subtitle,
    this.config,
    required super.item,
    super.filters,
    super.showHeader,
    super.showFilters,
    super.onRefresh,
  });

  @override
  Widget buildChartContent(BuildContext context, List<ChartData> data) {
    return WorkloadContent(
      title: title,
      subtitle: subtitle,
      config: config,
    );
  }
}

/// محتوى Workload Widget المنفصل
class WorkloadContent extends StatefulWidget {
  final String? title;
  final String? subtitle;
  final Map<String, dynamic>? config;

  const WorkloadContent({
    super.key,
    this.title,
    this.subtitle,
    this.config,
  });

  @override
  State<WorkloadContent> createState() => _WorkloadContentState();
}

class _WorkloadContentState extends State<WorkloadContent>
    with TickerProviderStateMixin {
  
  late final TaskController _taskController;
  late final UserController _userController;
  
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  
  List<WorkloadData> _workloadData = [];
  bool _isLoading = true;
  String _selectedView = 'current'; // current, weekly, monthly

  @override
  void initState() {
    super.initState();
    _initializeControllers();
    _initializeAnimations();
    _loadData();
  }

  void _initializeControllers() {
    _taskController = Get.find<TaskController>();
    _userController = Get.find<UserController>();
  }

  void _initializeAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  Future<void> _loadData() async {
    setState(() => _isLoading = true);

    try {
      final workloadList = <WorkloadData>[];
      final users = _userController.users.where((u) => u.isActive).toList();

      for (final user in users) {
        final userTasks = _taskController.allTasks.where((task) => 
          task.assigneeId == user.id && 
          !task.isDeleted &&
          _isTaskInSelectedPeriod(task)
        ).toList();

        final totalTasks = userTasks.length;
        final completedTasks = userTasks.where((task) => 
          task.status == 'completed').length;
        final inProgressTasks = userTasks.where((task) => 
          task.status == 'in_progress').length;
        final pendingTasks = userTasks.where((task) => 
          task.status == 'pending').length;
        final overdueTasks = userTasks.where((task) => 
          task.status == 'overdue' || _isTaskOverdue(task)).length;

        // حساب مستوى العبء
        final workloadLevel = _calculateWorkloadLevel(totalTasks, completedTasks);
        
        workloadList.add(WorkloadData(
          userName: user.name,
          userId: user.id,
          totalTasks: totalTasks,
          completedTasks: completedTasks,
          inProgressTasks: inProgressTasks,
          pendingTasks: pendingTasks,
          overdueTasks: overdueTasks,
          workloadLevel: workloadLevel,
          completionRate: totalTasks > 0 ? (completedTasks / totalTasks * 100) : 0.0,
        ));
      }

      // ترتيب حسب العبء
      workloadList.sort((a, b) => b.totalTasks.compareTo(a.totalTasks));

      setState(() {
        _workloadData = workloadList;
        _isLoading = false;
      });

      _animationController.forward();
    } catch (e) {
      debugPrint('خطأ في تحميل بيانات Workload Widget: $e');
      setState(() => _isLoading = false);
    }
  }

  bool _isTaskInSelectedPeriod(dynamic task) {
    final now = DateTime.now();
    final taskDate = task.createdAtDateTime;

    switch (_selectedView) {
      case 'current':
        return true; // جميع المهام
      case 'weekly':
        final weekStart = now.subtract(Duration(days: now.weekday - 1));
        return taskDate.isAfter(weekStart);
      case 'monthly':
        return taskDate.month == now.month && taskDate.year == now.year;
      default:
        return true;
    }
  }

  bool _isTaskOverdue(dynamic task) {
    if (task.dueDate == null) return false;
    final dueDate = DateTime.fromMillisecondsSinceEpoch(task.dueDate);
    return dueDate.isBefore(DateTime.now()) && task.status != 'completed';
  }

  WorkloadLevel _calculateWorkloadLevel(int totalTasks, int completedTasks) {
    if (totalTasks == 0) return WorkloadLevel.light;
    
    final pendingTasks = totalTasks - completedTasks;
    
    if (pendingTasks >= 15) return WorkloadLevel.overloaded;
    if (pendingTasks >= 10) return WorkloadLevel.heavy;
    if (pendingTasks >= 5) return WorkloadLevel.moderate;
    return WorkloadLevel.light;
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildHeader(),
          const SizedBox(height: 16),
          _buildViewSelector(),
          const SizedBox(height: 16),
          Expanded(
            child: _isLoading ? _buildLoadingState() : _buildWorkloadChart(),
          ),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: AppColors.primary.withOpacity(0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            Icons.work_outline,
            color: AppColors.primary,
            size: 20,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                widget.title ?? 'عبء العمل',
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Colors.black87,
                ),
              ),
              if (widget.subtitle != null) ...[
                const SizedBox(height: 4),
                Text(
                  widget.subtitle!,
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey.shade600,
                  ),
                ),
              ],
            ],
          ),
        ),
        IconButton(
          icon: const Icon(Icons.refresh, size: 20),
          onPressed: _loadData,
          tooltip: 'تحديث البيانات',
        ),
      ],
    );
  }

  Widget _buildViewSelector() {
    return Row(
      children: [
        _buildViewButton('current', 'الحالي'),
        const SizedBox(width: 8),
        _buildViewButton('weekly', 'أسبوعي'),
        const SizedBox(width: 8),
        _buildViewButton('monthly', 'شهري'),
      ],
    );
  }

  Widget _buildViewButton(String value, String label) {
    final isSelected = _selectedView == value;
    
    return GestureDetector(
      onTap: () {
        setState(() => _selectedView = value);
        _loadData();
      },
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
        decoration: BoxDecoration(
          color: isSelected ? AppColors.primary : Colors.grey.shade100,
          borderRadius: BorderRadius.circular(16),
        ),
        child: Text(
          label,
          style: TextStyle(
            fontSize: 12,
            color: isSelected ? Colors.white : Colors.black87,
            fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
          ),
        ),
      ),
    );
  }

  Widget _buildLoadingState() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(),
          SizedBox(height: 8),
          Text(
            'جاري تحميل بيانات العبء...',
            style: TextStyle(fontSize: 12, color: Colors.grey),
          ),
        ],
      ),
    );
  }

  Widget _buildWorkloadChart() {
    if (_workloadData.isEmpty) {
      return const Center(
        child: Text(
          'لا توجد بيانات عبء عمل للعرض',
          style: TextStyle(color: Colors.grey),
        ),
      );
    }

    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return FadeTransition(
          opacity: _fadeAnimation,
          child: Column(
            children: [
              // مخطط العبء
              Expanded(
                flex: 2,
                child: SfCartesianChart(
                  primaryXAxis: CategoryAxis(),
                  primaryYAxis: NumericAxis(),
                  series: <CartesianSeries>[
                    StackedColumnSeries<WorkloadData, String>(
                      name: 'مكتملة',
                      dataSource: _workloadData,
                      xValueMapper: (WorkloadData data, _) => data.userName,
                      yValueMapper: (WorkloadData data, _) => data.completedTasks,
                      color: Colors.green,
                    ),
                    StackedColumnSeries<WorkloadData, String>(
                      name: 'قيد التنفيذ',
                      dataSource: _workloadData,
                      xValueMapper: (WorkloadData data, _) => data.userName,
                      yValueMapper: (WorkloadData data, _) => data.inProgressTasks,
                      color: Colors.blue,
                    ),
                    StackedColumnSeries<WorkloadData, String>(
                      name: 'معلقة',
                      dataSource: _workloadData,
                      xValueMapper: (WorkloadData data, _) => data.userName,
                      yValueMapper: (WorkloadData data, _) => data.pendingTasks,
                      color: Colors.orange,
                    ),
                    StackedColumnSeries<WorkloadData, String>(
                      name: 'متأخرة',
                      dataSource: _workloadData,
                      xValueMapper: (WorkloadData data, _) => data.userName,
                      yValueMapper: (WorkloadData data, _) => data.overdueTasks,
                      color: Colors.red,
                    ),
                  ],
                  legend: Legend(isVisible: true, position: LegendPosition.bottom),
                ),
              ),
              // قائمة مستويات العبء
              Expanded(
                flex: 1,
                child: ListView.builder(
                  itemCount: _workloadData.length,
                  itemBuilder: (context, index) {
                    final data = _workloadData[index];
                    return _buildWorkloadItem(data);
                  },
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildWorkloadItem(WorkloadData data) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: data.workloadLevel.color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: data.workloadLevel.color.withOpacity(0.3),
        ),
      ),
      child: Row(
        children: [
          Icon(
            data.workloadLevel.icon,
            color: data.workloadLevel.color,
            size: 20,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  data.userName,
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 14,
                  ),
                ),
                Text(
                  '${data.totalTasks} مهمة - ${data.completionRate.toInt()}% مكتمل',
                  style: const TextStyle(fontSize: 12, color: Colors.grey),
                ),
              ],
            ),
          ),
          Text(
            data.workloadLevel.label,
            style: TextStyle(
              fontSize: 12,
              color: data.workloadLevel.color,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }
}

/// بيانات العبء للمستخدم
class WorkloadData {
  final String userName;
  final int userId;
  final int totalTasks;
  final int completedTasks;
  final int inProgressTasks;
  final int pendingTasks;
  final int overdueTasks;
  final WorkloadLevel workloadLevel;
  final double completionRate;

  WorkloadData({
    required this.userName,
    required this.userId,
    required this.totalTasks,
    required this.completedTasks,
    required this.inProgressTasks,
    required this.pendingTasks,
    required this.overdueTasks,
    required this.workloadLevel,
    required this.completionRate,
  });
}

/// مستويات العبء
enum WorkloadLevel {
  light('خفيف', Colors.green, Icons.sentiment_satisfied),
  moderate('متوسط', Colors.orange, Icons.sentiment_neutral),
  heavy('ثقيل', Colors.deepOrange, Icons.sentiment_dissatisfied),
  overloaded('محمل زائد', Colors.red, Icons.sentiment_very_dissatisfied);

  const WorkloadLevel(this.label, this.color, this.icon);
  
  final String label;
  final Color color;
  final IconData icon;
}
