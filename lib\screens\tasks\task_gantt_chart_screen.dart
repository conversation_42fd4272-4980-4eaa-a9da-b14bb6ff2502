// import 'package:flutter/material.dart';
// import 'package:get/get.dart';
// import 'package:intl/intl.dart';
// import '../../controllers/task_controller.dart';
// import '../../controllers/user_controller.dart';
// import '../../controllers/department_controller.dart';
// import '../../controllers/auth_controller.dart';
// import '../../models/task_models.dart';
// import '../../models/task_status_enum.dart';
// import '../../services/unified_permission_service.dart';

// /// شاشة مخطط جانت للمهام
// /// تعرض المهام في مخطط زمني يوضح العلاقات والمدد الزمنية
// /// ملاحظة: هذه نسخة مؤقتة حتى يتم تنفيذ مخطط جانت كامل
// class TaskGanttChartScreen extends StatefulWidget {
//   const TaskGanttChartScreen({super.key});

//   @override
//   State<TaskGanttChartScreen> createState() => _TaskGanttChartScreenState();
// }

// class _TaskGanttChartScreenState extends State<TaskGanttChartScreen> {
//   final TaskController _taskController = Get.find<TaskController>();
//   final UserController _userController = Get.find<UserController>();
//   final DepartmentController _departmentController = Get.find<DepartmentController>();

//   // بيانات المهام
//   List<Task> _tasks = [];

//   // متغيرات مخطط جانت
//   // GanttViewRange _ganttViewRange = GanttViewRange.month;
//   List<String>? _selectedDepartments;
//   List<String>? _selectedUsers;

//   // حالة التحميل
//   bool _isLoading = true;
//   String _errorMessage = '';

//   @override
//   void initState() {
//     super.initState();
//     _loadData();
//   }

//   // تحميل البيانات
//   Future<void> _loadData() async {
//     setState(() {
//       _isLoading = true;
//       _errorMessage = '';
//     });

//     try {
//       // الحصول على المهام حسب صلاحيات المستخدم
//       final authController = Get.find<AuthController>();
//       if (authController.currentUser.value != null) {
//         await _taskController.loadTasksByUserPermissions(authController.currentUser.value!.id, forceRefresh: true);
//       }
//       _tasks = _taskController.allTasks;

//       // تحميل المستخدمين والأقسام
//       await _userController.loadAllUsers();
//       await _departmentController.loadAllDepartments();

//       setState(() {
//         _isLoading = false;
//       });
//     } catch (e) {
//       setState(() {
//         _isLoading = false;
//         _errorMessage = 'حدث خطأ أثناء تحميل البيانات: $e';
//       });
//     }
//   }

//   @override
//   Widget build(BuildContext context) {
//     return Scaffold(
//       appBar: AppBar(
//         title: const Text('مخطط جانت للمهام'),
//         actions: [
//           IconButton(
//             icon: const Icon(Icons.refresh),
//             tooltip: 'تحديث',
//             onPressed: _loadData,
//           ),
//         ],
//       ),
//       body: _isLoading
//           ? const Center(child: CircularProgressIndicator())
//           : _errorMessage.isNotEmpty
//               ? _buildErrorWidget()
//               : _buildGanttChart(),
//     );
//   }

//   // بناء واجهة الخطأ
//   Widget _buildErrorWidget() {
//     return Center(
//       child: Column(
//         mainAxisAlignment: MainAxisAlignment.center,
//         children: [
//           const Icon(Icons.error_outline, size: 48, color: Colors.red),
//           const SizedBox(height: 16),
//           Text(
//             _errorMessage,
//             style: const TextStyle(fontSize: 16),
//             textAlign: TextAlign.center,
//           ),
//           const SizedBox(height: 16),
//           ElevatedButton(
//             onPressed: _loadData,
//             child: const Text('إعادة المحاولة'),
//           ),
//         ],
//       ),
//     );
//   }

//   // بناء مخطط جانت
//   Widget _buildGanttChart() {
//     // تصفية المهام النشطة فقط (غير المكتملة وغير الملغاة)
//     // وأيضًا تصفية المهام المحذوفة
//     final activeTasks = _tasks.where((task) =>
//       !task.isDeleted
//     ).toList();

//     // إذا لم تكن هناك مهام نشطة، عرض رسالة
//     if (activeTasks.isEmpty) {
//       return const Center(
//         child: Text(
//           'لا توجد مهام للعرض في مخطط جانت',
//           style: TextStyle(fontSize: 16),
//         ),
//       );
//     }

//     // تحديد تاريخ البدء المناسب للمهام
//     DateTime startDate;
//     if (activeTasks.isNotEmpty) {
//       // ابدأ بتاريخ أول مهمة
//       DateTime earliestDate = activeTasks[0].startDateDateTime ?? activeTasks[0].createdAtDateTime;

//       // ابحث عن أقدم تاريخ
//       for (final task in activeTasks) {
//         final taskStartDate = task.startDateDateTime ?? task.createdAtDateTime;
//         if (taskStartDate.isBefore(earliestDate)) {
//           earliestDate = taskStartDate;
//         }
//       }

//       // استخدم أول يوم في الشهر الذي يحتوي على أقدم تاريخ
//       startDate = DateTime(earliestDate.year, earliestDate.month, 1);
//     } else {
//       // إذا لم تكن هناك مهام، استخدم تاريخًا افتراضيًا
//       startDate = DateTime.now();
//     }

//     // إنشاء قاموس لأسماء المستخدمين
//     final Map<String, String> userNames = {};
//     for (final user in _userController.users) {
//       userNames[user.id.toString()] = user.name;
//     }

//     // إنشاء قاموس لأسماء الأقسام
//     final Map<String, String> departmentNames = {};
//     for (final department in _departmentController.allDepartments) {
//       departmentNames[department.id.toString()] = department.name;
//     }

//     return EnhancedGanttChart(
//       tasks: activeTasks,
//       title: 'مخطط جانت للمهام',
//       viewRange: _ganttViewRange,
//       startDate: startDate,
//       taskBarHeight: 36,
//       taskBarSpacing: 16,
//       taskNameWidth: 220,
//       departmentIds: _selectedDepartments,
//       userIds: _selectedUsers,
//       departmentNames: departmentNames,
//       userNames: userNames,
//       onViewRangeChanged: (newRange) {
//         setState(() {
//           _ganttViewRange = newRange;
//         });
//       },
//       onFilterChanged: (departments, users) {
//         setState(() {
//           _selectedDepartments = departments;
//           _selectedUsers = users;
//         });
//       },
//       onTaskTap: (task) {
//         _showTaskDetails(task, userNames, departmentNames);
//       },
//     );
//   }

//   // عرض تفاصيل المهمة
//   void _showTaskDetails(Task task, Map<String, String> userNames, Map<String, String> departmentNames) {
//     // الحصول على اسم المستخدم المعين للمهمة
//     String assigneeName = 'غير معين';
//     if (task.assigneeId != null && userNames.containsKey(task.assigneeId.toString())) {
//       assigneeName = userNames[task.assigneeId.toString()]!;
//     }

//     // الحصول على اسم القسم
//     String departmentName = 'غير محدد';
//     if (task.departmentId != null && departmentNames.containsKey(task.departmentId.toString())) {
//       departmentName = departmentNames[task.departmentId.toString()]!;
//     }

//     // تحويل حالة المهمة إلى نص
//     String statusText;
//     final taskStatus = TaskStatus.fromString(task.status);
//     switch (taskStatus) {
//       case TaskStatus.completed:
//         statusText = 'مكتملة';
//         break;
//       case TaskStatus.inProgress:
//         statusText = 'قيد التنفيذ';
//         break;
//       case TaskStatus.waitingForInfo:
//         statusText = 'في انتظار معلومات';
//         break;
//       case TaskStatus.pending:
//         statusText = 'قيد الانتظار';
//         break;
//       case TaskStatus.cancelled:
//         statusText = 'ملغاة';
//         break;
//       case TaskStatus.news:
//         statusText = 'جديدة';
//         break;
//       default:
//         statusText = 'غير معروفة';
//     }

//     showDialog(
//       context: context,
//       builder: (context) => AlertDialog(
//         title: Text(task.title),
//         content: Column(
//           mainAxisSize: MainAxisSize.min,
//           crossAxisAlignment: CrossAxisAlignment.start,
//           children: [
//             Text('الوصف: ${task.description}'),
//             const SizedBox(height: 8),
//             Text('القسم: $departmentName'),
//             Text('المعين: $assigneeName'),
//             const SizedBox(height: 8),
//             Text('الحالة: $statusText'),
//             Text('نسبة الإنجاز: ${task.completionPercentage}%'),
//             const SizedBox(height: 8),
//             Text('تاريخ البدء: ${task.startDateDateTime != null ? DateFormat('yyyy/MM/dd').format(task.startDateDateTime!) : 'غير محدد'}'),
//             Text('تاريخ الاستحقاق: ${task.dueDateDateTime != null ? DateFormat('yyyy/MM/dd').format(task.dueDateDateTime!) : 'غير محدد'}'),
//             const SizedBox(height: 8),
//             LinearProgressIndicator(
//               value: task.completionPercentage / 100,
//               backgroundColor: Colors.grey[300],
//               valueColor: AlwaysStoppedAnimation<Color>(_getTaskColor(task)),
//             ),
//           ],
//         ),
//         actions: [
//           TextButton(
//             onPressed: () => Navigator.of(context).pop(),
//             child: const Text('إغلاق'),
//           ),
//           TextButton(
//             onPressed: () {
//               // 🔒 فحص الصلاحيات قبل التنقل - إصلاح ثغرة أمنية
//               final permissionService = Get.find<UnifiedPermissionService>();
//               if (!permissionService.canViewTaskDetails()) {
//                 Navigator.of(context).pop();
//                 Get.snackbar(
//                   'غير مسموح',
//                   'ليس لديك صلاحية لعرض تفاصيل المهام',
//                   snackPosition: SnackPosition.BOTTOM,
//                   backgroundColor: Colors.red.shade100,
//                   colorText: Colors.red.shade800,
//                   duration: const Duration(seconds: 3),
//                 );
//                 return;
//               }

//               Navigator.of(context).pop();
//               Get.toNamed('/task-details', arguments: {'taskId': task.id});
//             },
//             child: const Text('عرض التفاصيل'),
//           ),
//         ],
//       ),
//     );
//   }

//   /// الحصول على لون المهمة حسب حالتها
//   Color _getTaskColor(Task task) {
//     final taskStatus = TaskStatus.fromString(task.status);
//     switch (taskStatus) {
//       case TaskStatus.completed:
//         return Colors.blue.shade600;
//       case TaskStatus.inProgress:
//         return Colors.green;
//       case TaskStatus.waitingForInfo:
//         return Colors.orange;
//       case TaskStatus.pending:
//         return Colors.amber;
//       case TaskStatus.cancelled:
//         return Colors.red;
//       case TaskStatus.news:
//         return Colors.indigo;
//       default:
//         return Colors.grey;
//     }
//   }

// }
