import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_application_2/screens/auth/login_screen.dart';
import 'package:flutter_application_2/screens/home/<USER>';
import 'package:flutter_application_2/services/unified_permission_service.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:get/get.dart';
// تم إزالة get_storage - سيتم استخدام shared_preferences

import 'services/storage_service.dart';
import 'services/system_settings_service.dart';
import 'services/admin/unified_admin_api_service.dart';
import 'controllers/auth_controller.dart';
import 'controllers/task_controller.dart';
import 'controllers/user_controller.dart';
import 'controllers/department_controller.dart';
import 'controllers/seed_data_controller.dart';
import 'controllers/task_messages_controller.dart';
import 'controllers/task_documents_controller.dart';
import 'controllers/notifications_controller.dart';
import 'controllers/admin_controller.dart';
// import 'services/signalr_service.dart'; // معلق مؤقتاً
// import 'services/simple_signalr_service.dart'; // معلق مؤقتاً
import 'services/unified_signalr_service.dart'; // الخدمة الموحدة الجديدة
import 'services/api/api_service.dart'; // إضافة استيراد ApiService
import 'services/task_documents_integration_service.dart';
import 'services/notifications_service.dart'; // إضافة استيراد NotificationsService
// سيتم تهيئة المتحكمات لاحقاً عند الحاجة
// import 'controllers/task_controller.dart';
// import 'controllers/user_controller.dart';
import 'constants/app_theme.dart';
import 'controllers/theme_controller.dart';
// تم إزالة الخدمات غير المتوافقة مع API
// سيتم استخدام API services بدلاً من الخدمات المحلية
// تم إزالة مستودع لوحة المعلومات
// تم إزالة خدمات قاعدة البيانات والتهجيرات
import 'localization/app_translations.dart';
import 'routes/app_routes.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // إضافة معالج للأخطاء غير المعالجة لتجنب crash التطبيق
  FlutterError.onError = (FlutterErrorDetails details) {
    // تجاهل خطأ SignalR المعروف
    if (details.exception.toString().contains('Null\' is not a subtype of type \'Object\'')) {
      debugPrint('⚠️ تم تجاهل خطأ SignalR المعروف: ${details.exception}');
      return;
    }
    // معالجة الأخطاء الأخرى بالطريقة العادية
    FlutterError.presentError(details);
  };

  // تم إزالة تهيئة إشعارات النظام من هنا - سيتم تهيئتها في MyApp.build

  await Get.putAsync(() => StorageService.init());
  Get.put(ThemeController());
  Get.put(RouteObserver<PageRoute>());
  Get.put(AuthController());
  // ⚠️ تم استبدال الخدمات المتعددة بخدمة موحدة
  // Get.put(SignalRService()); // معلق مؤقتاً
  // Get.put(SimpleSignalRService(), permanent: true); // معلق مؤقتاً

  // 🚀 الخدمة الموحدة الجديدة
  Get.put(UnifiedSignalRService(), permanent: true); // خدمة SignalR موحدة
  Get.put(TaskMessagesController(), permanent: true); // إضافة متحكم رسائل المهام
  Get.put(NotificationsController(), permanent: true); // إضافة متحكم الإشعارات

  // تسجيل ApiService في GetX
  Get.put(ApiService(), permanent: true);

  // تسجيل UnifiedAdminApiService (يحتاج StorageService)
  Get.put(UnifiedAdminApiService(), permanent: true);

  // تهيئة نظام المستندات
  Get.put(TaskDocumentsController(), permanent: true);
  Get.put(TaskDocumentsIntegrationService(), permanent: true);

  // تسجيل UnifiedPermissionService في GetX قبل AdminController
  Get.put(UnifiedPermissionService(), permanent: true);
  // تسجيل SystemSettingsService (يحتاج UnifiedAdminApiService)
  Get.put(SystemSettingsService(), permanent: true);
  // تسجيل AdminController في GetX
  Get.put(AdminController(), permanent: true);

  // تهيئة إصلاحات لوحة المفاتيح
  _initializeKeyboardFixes();
  
  // تهيئة البيانات الأساسية
  _initializeBasicData();
  
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    // تهيئة إشعارات النظام هنا باستخدام BuildContext الصحيح
    NotificationsService.initialize(context);
// NotificationsController notificationsController = Get.find<NotificationsController>();
    // notificationsController.loadNotifications(Get.find<AuthController>().currentUser.value?.id ?? 0); 
    return GetMaterialApp(
      title: 'نظام إدارة المهام',
      debugShowCheckedModeBanner: false,

      // إضافة مراقب المسارات
      navigatorObservers: [Get.find<RouteObserver<PageRoute>>()],

      // تم إزالة الروابط الأولية - سيتم تهيئة المتحكمات في main

      // Define routes
      getPages: AppRoutes.pages,

      // الترجمات
      translations: AppTranslations(),
      locale: const Locale('ar', ''),
      fallbackLocale: const Locale('ar', ''),

      // إعدادات السمة - استخدام قيم مباشرة بدلاً من الوصول إلى متغيرات تفاعلية
      theme: AppTheme.lightTheme,
      darkTheme: AppTheme.darkTheme,
      themeMode: Get.find<ThemeController>().isSystemTheme
          ? ThemeMode.system
          : (Get.find<ThemeController>().isDarkMode
              ? ThemeMode.dark
              : ThemeMode.light),

      // دعم التوطين
      localizationsDelegates: const [
        GlobalMaterialLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
      ],
      supportedLocales: const [
        Locale('ar', ''), // العربية
      ],

      // اتجاه النص من اليمين إلى اليسار
      textDirection: TextDirection.rtl,

      // تطبيق الاتجاه من اليمين إلى اليسار
      builder: (context, child) {
        return Directionality(
          textDirection: TextDirection.rtl,
          child: child!,
        );
      },

      // Check if user is logged in and show appropriate screen
      home: _getInitialScreen(),
    );
  }
}

/// إصلاح مشاكل لوحة المفاتيح
void _initializeKeyboardFixes() {
  try {
    // إضافة معالج مخصص لأحداث لوحة المفاتيح
    HardwareKeyboard.instance.addHandler(_handleGlobalKeyEvent);

    debugPrint('تم تهيئة إصلاحات لوحة المفاتيح بنجاح');
  } catch (e) {
    debugPrint('خطأ في تهيئة إصلاحات لوحة المفاتيح: $e');
  }
}

/// معالج عام لأحداث لوحة المفاتيح
bool _handleGlobalKeyEvent(KeyEvent event) {
  try {
    // معالجة خاصة فقط لمفاتيح Alt لحل مشكلة Alt Left`
    if (event is KeyDownEvent) {
      final physicalKey = event.physicalKey;

      // تجاهل أحداث Alt المكررة فقط
      if (physicalKey == PhysicalKeyboardKey.altLeft ||
          physicalKey == PhysicalKeyboardKey.altRight) {
        final pressedKeys = HardwareKeyboard.instance.physicalKeysPressed;
        if (pressedKeys.contains(physicalKey)) {
          debugPrint('تجاهل حدث Alt مكرر: ${physicalKey.debugName}');
          return true;
        }
      }
    }

    // السماح بمعالجة جميع الأحداث الأخرى بشكل طبيعي
    // هذا يتيح للحقول النصية استقبال أحداث الكتابة
    return false;
  } catch (e) {
    debugPrint('خطأ في معالجة حدث لوحة المفاتيح: $e');
    // في حالة الخطأ، السماح بمعالجة الحدث بشكل طبيعي
    return false;
  }
}

// تم حذف دالة initializeDatabase - سيتم استخدام API بدلاً من قاعدة البيانات المحلية

// تم حذف دالة _createDefaultUserIfNeeded - سيتم إدارة المستخدمين عبر API

/// تهيئة البيانات الأساسية في الخلفية
void _initializeBasicData() {
  // تشغيل في الخلفية لتجنب تأخير بدء التطبيق
  Future.delayed(const Duration(seconds: 2), () async {
    try {
      debugPrint('بدء تهيئة البيانات الأساسية...');

      // تهيئة SeedDataController إذا لم يكن موجوداً
      if (!Get.isRegistered<SeedDataController>()) {
        Get.put(SeedDataController(), permanent: true);
      }

      final seedController = Get.find<SeedDataController>();

      // محاولة إنشاء البيانات الأساسية إذا لم تكن موجودة
      await seedController.seedTaskStatuses();
      await seedController.seedTaskPriorities();

      debugPrint('تم الانتهاء من تهيئة البيانات الأساسية');
    } catch (e) {
      debugPrint('خطأ في تهيئة البيانات الأساسية: $e');
      // لا نريد إيقاف التطبيق بسبب هذا الخطأ
    }
  });
}

/// تحديد الشاشة الأولى بناءً على حالة تسجيل الدخول
Widget _getInitialScreen() {
  try {
    final authController = Get.find<AuthController>();
    if (authController.isLoggedIn) {
      // إذا كان المستخدم مسجل دخول، اذهب للشاشة الرئيسية التي تحتوي على الداشبورد
      return FutureBuilder(
        future: _initializeDashboardScreen(),
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.done) {
            // توجيه إلى HomeScreen التي تحتوي على ModernDashboardScreen في التبويب الأول
            return const HomeScreen();
          }
          return const Scaffold(
            body: Center(
              child: CircularProgressIndicator(),
            ),
          );
        },
      );
    }
  } catch (e) {
    debugPrint('خطأ في التحقق من حالة تسجيل الدخول: $e');
  }

  // إذا لم يكن مسجل دخول أو حدث خطأ، اذهب لشاشة تسجيل الدخول
  return const LoginScreen();
}

/// تهيئة لوحة التحكم الرئيسية
Future<void> _initializeDashboardScreen() async {
  try {
    final authController = Get.find<AuthController>();
    if (authController.currentUser.value != null) {
      // تهيئة المتحكمات المطلوبة للوحة التحكم
      if (!Get.isRegistered<TaskController>()) {
        Get.put(TaskController(), permanent: true);
      }
      if (!Get.isRegistered<UserController>()) {
        Get.put(UserController(), permanent: true);
      }
      if (!Get.isRegistered<DepartmentController>()) {
        Get.put(DepartmentController(), permanent: true);
      }
      debugPrint('تم تهيئة لوحة التحكم الرئيسية بنجاح');
    }
  } catch (e) {
    debugPrint('خطأ في تهيئة لوحة التحكم الرئيسية: $e');
  }
}
